# 📁 Guia de Configuração do Storage para Imagens de Drone

Como as políticas de storage precisam ser criadas via Dashboard do Supabase (devido a permissões), siga este guia passo a passo:

## 🚀 Passo 1: Executar a Migração SQL

Execute apenas esta migração no SQL Editor do Supabase:

```sql
-- Execute: supabase/migrations/20250624000400_simple_drone_images_update.sql
```

Esta migração adiciona as colunas necessárias à tabela `drone_images` e cria as funções auxiliares.

## 🗂️ Passo 2: Criar o Bucket de Storage

1. **Acesse o Dashboard do Supabase**: https://uoeqlticflhaagkbajnu.supabase.co
2. **Vá para Storage** (menu lateral)
3. **Clique em "Create bucket"**
4. **Configure o bucket**:
   - **Name**: `drone-images`
   - **Public bucket**: ✅ **Ativado** (para URLs públicas)
   - **File size limit**: `52428800` (50MB)
   - **Allowed MIME types**: 
     ```
     image/jpeg
     image/png  
     image/tiff
     image/webp
     ```

## 🔒 Passo 3: Criar Políticas de Segurança

Após criar o bucket, vá para a aba **Policies** do bucket `drone-images` e crie as seguintes políticas:

### 📖 Política 1: Visualizar Imagens
- **Policy name**: `Users can view drone images from their farms`
- **Allowed operation**: `SELECT`
- **Target roles**: `authenticated`
- **USING expression**:
```sql
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)
```

### 📤 Política 2: Upload de Imagens
- **Policy name**: `Users can upload drone images to their farms`
- **Allowed operation**: `INSERT`
- **Target roles**: `authenticated`
- **WITH CHECK expression**:
```sql
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)
```

### ✏️ Política 3: Atualizar Imagens
- **Policy name**: `Users can update drone images from their farms`
- **Allowed operation**: `UPDATE`
- **Target roles**: `authenticated`
- **USING expression**:
```sql
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)
```

### 🗑️ Política 4: Deletar Imagens
- **Policy name**: `Users can delete drone images from their farms`
- **Allowed operation**: `DELETE`
- **Target roles**: `authenticated`
- **USING expression**:
```sql
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)
```

## 📂 Estrutura de Pastas

O sistema organizará as imagens na seguinte estrutura:
```
drone-images/
├── 1/                    # farm_id = 1
│   ├── 1735084800_abc123.jpg
│   └── 1735084801_def456.png
├── 2/                    # farm_id = 2
│   ├── 1735084802_ghi789.tiff
│   └── 1735084803_jkl012.jpg
└── 3/                    # farm_id = 3
    └── 1735084804_mno345.png
```

## ✅ Verificação da Configuração

Após configurar tudo, teste se está funcionando:

1. **Faça login na aplicação**
2. **Vá para "Análise de Imagens"**
3. **Selecione uma fazenda**
4. **Tente fazer upload de uma imagem**
5. **Verifique se a imagem aparece na lista**

## 🔧 Funcionalidades Disponíveis

Após a configuração completa, você terá:

- ✅ **Upload real de imagens** para o Supabase Storage
- ✅ **URLs públicas** para visualizar as imagens
- ✅ **Organização por fazenda** (cada fazenda tem sua pasta)
- ✅ **Controle de acesso** (usuários só veem suas próprias imagens)
- ✅ **Análise simulada** com resultados mock
- ✅ **Metadados completos** (tamanho, dimensões, etc.)
- ✅ **Estatísticas de uso** de storage por fazenda
- ✅ **Cleanup automático** ao deletar imagens

## 🚨 Troubleshooting

### Erro de Upload
- Verifique se o bucket foi criado corretamente
- Confirme se as políticas foram aplicadas
- Verifique se o usuário está autenticado

### Erro de Permissão
- Confirme se as políticas estão usando as expressões SQL corretas
- Verifique se o usuário possui fazendas cadastradas

### Imagens não aparecem
- Verifique se a coluna `image_url` foi adicionada à tabela
- Confirme se o bucket está público
- Verifique se as URLs estão sendo geradas corretamente

## 📞 Suporte

Se encontrar problemas, verifique:
1. Console do navegador para erros JavaScript
2. Logs do Supabase no Dashboard
3. Políticas de RLS nas tabelas `farms` e `drone_images`
