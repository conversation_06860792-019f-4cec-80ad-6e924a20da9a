import React, { useState, useEffect } from 'react';
import {
  Sprout,
  TrendingUp,
  Camera,
  BarChart3,
  Shield,
  Users,
  Check,
  Star,
  Menu,
  X,
  ArrowRight,
  Leaf,
  MapPin,
  Clock,
  Database,
  Zap
} from 'lucide-react';

// Importar componentes do dashboard
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import MapViewPrecision from './components/MapViewPrecision';
import Analytics from './components/Analytics';
import Reports from './components/Reports';
import FarmManagement from './components/FarmManagement';
import DroneImageAnalysis from './components/DroneImageAnalysis';
import AuthModal from './components/AuthModal';

// Importar hooks
import { useAuth } from './hooks/useAuth';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('mensal');
  const [currentView, setCurrentView] = useState('landing');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  const { user, loading, signOut } = useAuth();

  // Função para renderizar a view atual do dashboard
  const renderDashboardView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'mapa':
        return <MapViewPrecision />;
      case 'fazendas':
        return <FarmManagement />;
      case 'analise-imagens':
        return <DroneImageAnalysis />;
      case 'analytics':
        return <Analytics />;
      case 'relatorios':
        return <Reports />;
      default:
        return <Dashboard />;
    }
  };

  // Função para fazer login (abre modal)
  const handleLogin = () => {
    setAuthMode('signin');
    setShowAuthModal(true);
  };

  // Função para criar conta (abre modal)
  const handleSignUp = () => {
    setAuthMode('signup');
    setShowAuthModal(true);
  };

  // Função para logout
  const handleLogout = async () => {
    await signOut();
    setCurrentView('landing');
  };

  // Função para alternar sidebar no mobile
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Redirecionar para dashboard quando usuário fizer login
  useEffect(() => {
    if (user && currentView === 'landing') {
      setCurrentView('dashboard');
    }
  }, [user, currentView]);

  // Mostrar loading enquanto verifica autenticação
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-emerald-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  // Se estiver logado, mostrar o dashboard
  if (user) {
    return (
      <div className="flex h-screen bg-gray-50 overflow-hidden">
        {/* Overlay para mobile */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={`
          fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
          transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 transition-transform duration-300 ease-in-out
        `}>
          <Sidebar
            currentView={currentView}
            onViewChange={(view) => {
              setCurrentView(view);
              setIsSidebarOpen(false); // Fechar sidebar no mobile após seleção
            }}
          />
        </div>

        {/* Main Content */}
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="p-3 sm:p-4 border-b border-gray-200 bg-white">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                {/* Menu button para mobile */}
                <button
                  onClick={toggleSidebar}
                  className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                >
                  <Menu className="w-5 h-5" />
                </button>
                <h2 className="text-base sm:text-lg font-semibold text-gray-900">
                  {currentView === 'dashboard' && 'Dashboard'}
                  {currentView === 'mapa' && 'Mapa Interativo'}
                  {currentView === 'fazendas' && 'Gestão de Fazendas'}
                  {currentView === 'analise-imagens' && 'Análise de Imagens'}
                  {currentView === 'analytics' && 'Análise Avançada'}
                  {currentView === 'relatorios' && 'Relatórios'}
                </h2>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-600 hidden sm:block">
                  {user.email}
                </span>
                <button
                  onClick={handleLogout}
                  className="text-gray-600 hover:text-gray-900 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors text-sm sm:text-base"
                >
                  Sair
                </button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto">
            {renderDashboardView()}
          </div>
        </main>
      </div>
    );
  }

  const features = [
    {
      icon: <Camera className="w-8 h-8 text-green-400" />,
      title: "Monitoramento por Drone",
      description: "Imagens aéreas de alta resolução com câmeras espectrais e infravermelhas para análise detalhada das culturas."
    },
    {
      icon: <Zap className="w-8 h-8 text-green-400" />,
      title: "Análise Espectral Avançada",
      description: "Processamento de imagens espectrais para identificar estresse hídrico, deficiências nutricionais e pragas."
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-green-400" />,
      title: "Análise de Dados Avançada",
      description: "Transforme dados das imagens de drone em insights acionáveis para maximizar a produtividade."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-green-400" />,
      title: "Previsão de Safras",
      description: "Algoritmos de IA para prever rendimentos e otimizar o planejamento de colheita baseado em imagens aéreas."
    },
    {
      icon: <Shield className="w-8 h-8 text-green-400" />,
      title: "Detecção Precoce de Problemas",
      description: "Identificação precoce de doenças, pragas e deficiências através de análise espectral para ação preventiva."
    },
    {
      icon: <MapPin className="w-8 h-8 text-green-400" />,
      title: "Mapeamento de Precisão",
      description: "Mapas detalhados de variabilidade das culturas e aplicação localizada de insumos baseados em dados de drone."
    }
  ];

  const plans = {
    mensal: [
      {
        name: "Iniciante",
        price: "R$ 397",
        period: "/mês",
        description: "Perfeito para pequenos produtores",
        features: [
          "Até 300 hectares",
          "2 voos de drone por mês",
          "Análise básica espectral",
          "Relatórios mensais",
          "Suporte via email",
          "1 usuário"
        ],
        popular: false
      },
      {
        name: "Profissional",
        price: "R$ 897",
        period: "/mês",
        description: "Ideal para fazendas médias",
        features: [
          "Até 500 hectares",
          "4 voos de drone por mês",
          "Análise espectral completa",
          "Câmeras infravermelhas",
          "Relatórios semanais",
          "Suporte prioritário",
          "Até 5 usuários",
          
        ],
        popular: true
      },
      {
        name: "Enterprise",
        price: "R$ 1.897",
        period: "/mês",
        description: "Para grandes operações",
        features: [
          "Hectares ilimitados",
          "10 voos de drone por mês",
          "Todas as funcionalidades",
          "Análise personalizada",
          "Relatórios após operações do drone",
          "Suporte 24/7",
          "Usuários ilimitados",
          "Integração customizada",
          "Acesso para seu agrônomo"
        ],
        popular: false
      }
    ],
    anual: [
      {
        name: "Iniciante",
        price: "R$ 3.573",
        period: "/ano",
        description: "Perfeito para pequenos produtores",
        features: [
          "Até 300 hectares",
          "2 voos de drone por mês",
          "Análise básica espectral",
          "Relatórios mensais",
          "Suporte via email",
          "1 usuário",
          "2 meses grátis"
        ],
        popular: false,
        savings: "Economize R$ 1.191"
      },
      {
        name: "Profissional",
        price: "R$ 8.073",
        period: "/ano",
        description: "Ideal para fazendas médias",
        features: [
          "Até 500 hectares",
          "4 voos de drone por mês",
          "Análise espectral completa",
          "Câmeras infravermelhas",
          "Relatórios semanais",
          "Suporte prioritário",
          "Até 5 usuários",
          "2 meses grátis"
        ],
        popular: true,
        savings: "Economize R$ 2.691"
      },
      {
        name: "Enterprise",
        price: "R$ 17.073",
        period: "/ano",
        description: "Para grandes operações",
        features: [
          "Hectares ilimitados",
          "10 voos de drone por mês",
          "Todas as funcionalidades",
          "Análise personalizada",
          "Relatórios após operações do drone",
          "Suporte 24/7",
          "Usuários ilimitados",
          "Integração customizada",
          "Acesso para seu agrônomo",
          "2 meses grátis"
        ],
        popular: false,
        savings: "Economize R$ 5.691"
      }
    ]
  };

  // TODO: Replace with actual testimonials data from API
  const testimonials: any[] = [];

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="fixed top-0 w-full bg-slate-900/95 backdrop-blur-sm border-b border-slate-800 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <Sprout className="w-8 h-8 text-green-400" />
              <span className="text-xl font-bold">AgroPrecision</span>
            </div>
            
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-green-400 transition-colors">Recursos</a>
              <a href="#pricing" className="hover:text-green-400 transition-colors">Preços</a>
              <a href="#testimonials" className="hover:text-green-400 transition-colors">Depoimentos</a>
              <a href="#contact" className="hover:text-green-400 transition-colors">Contato</a>
            </nav>

            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={handleLogin}
                className="text-slate-300 hover:text-white transition-colors"
              >
                Entrar
              </button>
              <button
                onClick={handleSignUp}
                className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors"
              >
                Teste Grátis
              </button>
            </div>

            <button 
              className="md:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-slate-800 border-t border-slate-700">
            <div className="px-4 py-2 space-y-2">
              <a href="#features" className="block py-2 hover:text-green-400">Recursos</a>
              <a href="#pricing" className="block py-2 hover:text-green-400">Preços</a>
              <a href="#testimonials" className="block py-2 hover:text-green-400">Depoimentos</a>
              <a href="#contact" className="block py-2 hover:text-green-400">Contato</a>
              <div className="pt-2 border-t border-slate-600">
                <button
                  onClick={handleLogin}
                  className="block w-full text-left py-2 text-slate-300"
                >
                  Entrar
                </button>
                <button
                  onClick={handleSignUp}
                  className="w-full mt-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg"
                >
                  Teste Grátis
                </button>
              </div>
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-12 sm:pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 leading-tight">
              Monitore sua Fazenda com
              <span className="text-green-400 block mt-2">Drones Espectrais</span>
              <span className="block mt-2">de Alta Precisão</span>
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-slate-300 mb-6 sm:mb-8 leading-relaxed px-4 sm:px-0">
              Câmeras espectrais e infravermelhas capturam dados invisíveis ao olho humano.
              Detecte problemas antes que apareçam e aumente sua produtividade em até 40%.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4 sm:px-0">
              <button
                onClick={handleSignUp}
                className="w-full sm:w-auto bg-green-600 hover:bg-green-700 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              >
                Comece Gratuitamente
                <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5" />
              </button>
              <button
                onClick={handleLogin}
                className="w-full sm:w-auto border border-slate-600 hover:border-slate-500 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-colors"
              >
                Ver Demonstração
              </button>
            </div>
          </div>

          <div className="mt-12 sm:mt-16 relative px-4 sm:px-0">
            <div className="bg-gradient-to-r from-green-900/20 to-slate-800/20 rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 backdrop-blur-sm border border-slate-700">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 text-center">
                <div className="py-2">
                  <div className="text-2xl sm:text-3xl font-bold text-green-400 mb-1 sm:mb-2">40%</div>
                  <div className="text-sm sm:text-base text-slate-300">Aumento na Produtividade</div>
                </div>
                <div className="py-2">
                  <div className="text-2xl sm:text-3xl font-bold text-green-400 mb-1 sm:mb-2">30%</div>
                  <div className="text-sm sm:text-base text-slate-300">Redução de Custos</div>
                </div>
                <div className="py-2">
                  <div className="text-2xl sm:text-3xl font-bold text-green-400 mb-1 sm:mb-2">99%</div>
                  <div className="text-sm sm:text-base text-slate-300">Precisão nas Análises</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4">
              Tecnologia de Drone
              <span className="text-green-400 block mt-2">que Transforma sua Fazenda</span>
            </h2>
            <p className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto px-4 sm:px-0">
              Nossa frota de drones equipados com câmeras espectrais e infravermelhas
              revela informações invisíveis sobre suas culturas.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-slate-800 rounded-xl p-4 sm:p-6 border border-slate-700 hover:border-green-400/50 transition-all hover:transform hover:-translate-y-1">
                <div className="mb-3 sm:mb-4">{feature.icon}</div>
                <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3">{feature.title}</h3>
                <p className="text-sm sm:text-base text-slate-300 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4">
              Planos que Crescem
              <span className="text-green-400 block mt-2">com sua Fazenda</span>
            </h2>
            <p className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto mb-6 sm:mb-8 px-4 sm:px-0">
              Escolha o plano ideal para o tamanho da sua operação.
              Todos incluem voos de drone, análise espectral e suporte completo.
            </p>

            <div className="flex justify-center mb-6 sm:mb-8">
              <div className="bg-slate-800 rounded-lg p-1 border border-slate-700 w-full max-w-sm sm:w-auto">
                <button
                  onClick={() => setSelectedPlan('mensal')}
                  className={`w-1/2 sm:w-auto px-4 sm:px-6 py-2 rounded-md transition-all text-sm sm:text-base ${
                    selectedPlan === 'mensal'
                      ? 'bg-green-600 text-white'
                      : 'text-slate-300 hover:text-white'
                  }`}
                >
                  Mensal
                </button>
                <button
                  onClick={() => setSelectedPlan('anual')}
                  className={`w-1/2 sm:w-auto px-4 sm:px-6 py-2 rounded-md transition-all text-sm sm:text-base ${
                    selectedPlan === 'anual'
                      ? 'bg-green-600 text-white'
                      : 'text-slate-300 hover:text-white'
                  }`}
                >
                  Anual
                  <span className="ml-1 sm:ml-2 text-xs bg-green-400 text-slate-900 px-1 sm:px-2 py-1 rounded-full">
                    -25%
                  </span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {plans[selectedPlan as keyof typeof plans].map((plan, index) => (
              <div key={index} className={`relative bg-slate-800 rounded-xl p-4 sm:p-6 lg:p-8 border transition-all hover:transform hover:-translate-y-1 ${
                plan.popular
                  ? 'border-green-400 ring-2 ring-green-400/20'
                  : 'border-slate-700 hover:border-green-400/50'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-600 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-semibold">
                      Mais Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-4 sm:mb-6">
                  <h3 className="text-xl sm:text-2xl font-bold mb-2">{plan.name}</h3>
                  <p className="text-sm sm:text-base text-slate-300 mb-3 sm:mb-4">{plan.description}</p>
                  <div className="mb-2">
                    <span className="text-3xl sm:text-4xl font-bold">{plan.price}</span>
                    <span className="text-sm sm:text-base text-slate-300">{plan.period}</span>
                  </div>
                  {'savings' in plan && plan.savings && (
                    <div className="text-green-400 text-xs sm:text-sm font-semibold">
                      {plan.savings}
                    </div>
                  )}
                </div>

                <ul className="space-y-2 sm:space-y-3 mb-6 sm:mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <Check className="w-4 sm:w-5 h-4 sm:h-5 text-green-400 mr-2 sm:mr-3 flex-shrink-0 mt-0.5" />
                      <span className="text-sm sm:text-base text-slate-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  onClick={handleSignUp}
                  className={`w-full py-2 sm:py-3 rounded-lg text-sm sm:text-base font-semibold transition-all ${
                    plan.popular
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'border border-slate-600 hover:border-green-400 hover:text-green-400'
                  }`}
                >
                  Começar Agora
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4">
              Produtores que Confiam
              <span className="text-green-400 block mt-2">em Nossa Tecnologia</span>
            </h2>
            <p className="text-lg sm:text-xl text-slate-300 max-w-3xl mx-auto px-4 sm:px-0">
              Veja como nossos drones espectrais estão transformando fazendas em todo o Brasil com resultados reais.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-slate-800 rounded-xl p-4 sm:p-6 border border-slate-700">
                <div className="flex mb-3 sm:mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 sm:w-5 h-4 sm:h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-sm sm:text-base text-slate-300 mb-4 sm:mb-6 italic">"{testimonial.content}"</p>
                <div className="border-t border-slate-700 pt-3 sm:pt-4">
                  <div className="text-sm sm:text-base font-semibold">{testimonial.name}</div>
                  <div className="text-slate-400 text-xs sm:text-sm">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-green-900/30 to-slate-800/30 rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-12 border border-slate-700">
            <Camera className="w-12 sm:w-16 h-12 sm:h-16 text-green-400 mx-auto mb-4 sm:mb-6" />
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6">
              Pronto para Ver sua Fazenda
              <span className="text-green-400 block mt-2">com Outros Olhos?</span>
            </h2>
            <p className="text-lg sm:text-xl text-slate-300 mb-6 sm:mb-8 max-w-2xl mx-auto px-4 sm:px-0">
              Junte-se a milhares de produtores que já descobriram o poder das câmeras espectrais.
              Veja o que seus olhos não conseguem detectar.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
              <button
                onClick={handleSignUp}
                className="w-full sm:w-auto bg-green-600 hover:bg-green-700 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              >
                Teste Gratuito por 30 Dias
                <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5" />
              </button>
              <button 
                onClick={handleLogin}
                className="w-full sm:w-auto border border-slate-600 hover:border-slate-500 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-colors flex items-center justify-center gap-2"
              >
                <Clock className="w-4 sm:w-5 h-4 sm:h-5" />
                Agendar Demonstração
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-slate-900 border-t border-slate-800 py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <div className="col-span-1 sm:col-span-2">
              <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                <Sprout className="w-6 sm:w-8 h-6 sm:h-8 text-green-400" />
                <span className="text-lg sm:text-xl font-bold">AgroPrecision</span>
              </div>
              <p className="text-sm sm:text-base text-slate-300 mb-4 sm:mb-6 max-w-md">
                Transformando a agricultura brasileira com drones espectrais de alta tecnologia.
                Sua fazenda mais produtiva e sustentável através de análises invisíveis.
              </p>
              <div className="flex space-x-3 sm:space-x-4">
                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-4 sm:w-5 h-4 sm:h-5" />
                </div>
                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-4 sm:w-5 h-4 sm:h-5" />
                </div>
                <div className="w-8 sm:w-10 h-8 sm:h-10 bg-slate-800 rounded-full flex items-center justify-center hover:bg-green-600 cursor-pointer transition-colors">
                  <Users className="w-4 sm:w-5 h-4 sm:h-5" />
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm sm:text-base font-semibold mb-3 sm:mb-4 text-green-400">Produto</h4>
              <ul className="space-y-1 sm:space-y-2 text-slate-300">
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Recursos</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Preços</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Integrações</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">API</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-sm sm:text-base font-semibold mb-3 sm:mb-4 text-green-400">Suporte</h4>
              <ul className="space-y-1 sm:space-y-2 text-slate-300">
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Documentação</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Centro de Ajuda</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Contato</a></li>
                <li><a href="#" className="text-sm sm:text-base hover:text-white transition-colors">Status</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-6 sm:mt-8 pt-6 sm:pt-8 text-center text-slate-400">
            <p className="text-sm sm:text-base">&copy; 2025 AgroPrecision. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onModeChange={setAuthMode}
      />
    </div>
  );
}

export default App;