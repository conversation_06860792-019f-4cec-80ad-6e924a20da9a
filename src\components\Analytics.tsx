import React, { useState } from 'react';
import { Bar<PERSON>hart3, TrendingUp, Calendar, Download, Filter, RefreshCw, MapPin, Layers, Eye, Settings, Target, Activity } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, AreaChart, Area, ScatterChart, Scatter } from 'recharts';

const Analytics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('ndvi');
  const [selectedFarm, setSelectedFarm] = useState('all');
  const [selectedCrop, setSelectedCrop] = useState('all');
  const [selectedAnalysisType, setSelectedAnalysisType] = useState('trend');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDataSource, setSelectedDataSource] = useState('all');
  const [selectedSeverity, setSelectedSeverity] = useState('all');

  // Opções de seleção
  const farms = [
    { id: 'all', name: 'Todas as Fazendas' },
    { id: 'sao-joao', name: 'Fazenda São João' },
    { id: 'esperanca', name: 'Fazenda Esperança' },
    { id: 'progresso', name: 'Fazenda Progresso' }
  ];

  const crops = [
    { id: 'all', name: 'Todas as Culturas' },
    { id: 'soja', name: 'Soja' },
    { id: 'milho', name: 'Milho' },
    { id: 'algodao', name: 'Algodão' }
  ];

  const analysisTypes = [
    { id: 'trend', name: 'Análise de Tendência', icon: TrendingUp },
    { id: 'correlation', name: 'Análise de Correlação', icon: Activity },
    { id: 'anomaly', name: 'Detecção de Anomalias', icon: Target },
    { id: 'productivity', name: 'Análise de Produtividade', icon: BarChart3 }
  ];

  const dataSources = [
    { id: 'all', name: 'Todas as Fontes' },
    { id: 'drone', name: 'Imagens de Drone' },
    { id: 'satellite', name: 'Dados de Satélite' },
    { id: 'sensor', name: 'Sensores IoT' },
    { id: 'weather', name: 'Dados Meteorológicos' }
  ];

  const severityLevels = [
    { id: 'all', name: 'Todas as Severidades' },
    { id: 'low', name: 'Baixa' },
    { id: 'medium', name: 'Média' },
    { id: 'high', name: 'Alta' },
    { id: 'critical', name: 'Crítica' }
  ];

  // TODO: Replace with actual analytics data from API
  const ndviTrend: any[] = [];
  const productivityData: any[] = [];
  const correlationData: any[] = [];
  const anomalyData: any[] = [];
  const insights: any[] = [];

  return (
    <div className="p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">Análise Avançada</h1>
              <p className="text-sm sm:text-base text-gray-600">Insights detalhados e correlações dos dados agrícolas</p>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-3 sm:px-4 py-2 rounded-lg transition-colors flex items-center space-x-1 sm:space-x-2 text-sm sm:text-base ${
                  showFilters
                    ? 'bg-emerald-100 text-emerald-700 border border-emerald-300'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Filter className="w-3 sm:w-4 h-3 sm:h-4" />
                <span className="hidden sm:inline">Filtros</span>
              </button>
              <button className="bg-emerald-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-1 sm:space-x-2 text-sm sm:text-base">
                <Download className="w-3 sm:w-4 h-3 sm:h-4" />
                <span className="hidden sm:inline">Exportar</span>
              </button>
            </div>
          </div>

          {/* Filtros Avançados */}
          {showFilters && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-4 sm:mb-6">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Filtros de Análise</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-3 sm:mb-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Período</label>
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="1month">Último mês</option>
                    <option value="3months">Últimos 3 meses</option>
                    <option value="6months">Últimos 6 meses</option>
                    <option value="1year">Último ano</option>
                    <option value="2years">Últimos 2 anos</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Fazenda</label>
                  <select
                    value={selectedFarm}
                    onChange={(e) => setSelectedFarm(e.target.value)}
                    className="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    {farms.map(farm => (
                      <option key={farm.id} value={farm.id}>{farm.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Cultura</label>
                  <select
                    value={selectedCrop}
                    onChange={(e) => setSelectedCrop(e.target.value)}
                    className="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    {crops.map(crop => (
                      <option key={crop.id} value={crop.id}>{crop.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Fonte de Dados</label>
                  <select
                    value={selectedDataSource}
                    onChange={(e) => setSelectedDataSource(e.target.value)}
                    className="w-full px-2 sm:px-3 py-1 sm:py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    {dataSources.map(source => (
                      <option key={source.id} value={source.id}>{source.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Tipo de Análise */}
              <div className="mb-3 sm:mb-4">
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">Tipo de Análise</label>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
                  {analysisTypes.map(type => {
                    const Icon = type.icon;
                    return (
                      <button
                        key={type.id}
                        onClick={() => setSelectedAnalysisType(type.id)}
                        className={`flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 rounded-lg border transition-colors ${
                          selectedAnalysisType === type.id
                            ? 'border-emerald-500 bg-emerald-50 text-emerald-700'
                            : 'border-gray-200 hover:border-gray-300 text-gray-700'
                        }`}
                      >
                        <Icon className="w-4 sm:w-5 h-4 sm:h-5 flex-shrink-0" />
                        <span className="text-xs sm:text-sm font-medium truncate">{type.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Métricas */}
              <div className="mb-3 sm:mb-4">
                <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">Métrica Principal</label>
                <div className="flex flex-wrap gap-1 sm:gap-2">
                  {['ndvi', 'productivity', 'rainfall', 'temperature', 'humidity', 'soil_moisture'].map(metric => (
                    <button
                      key={metric}
                      onClick={() => setSelectedMetric(metric)}
                      className={`px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
                        selectedMetric === metric
                          ? 'bg-emerald-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {metric === 'ndvi' ? 'NDVI' :
                       metric === 'productivity' ? 'Produtividade' :
                       metric === 'rainfall' ? 'Precipitação' :
                       metric === 'temperature' ? 'Temperatura' :
                       metric === 'humidity' ? 'Umidade' : 'Umidade do Solo'}
                    </button>
                  ))}
                </div>
              </div>

              {/* Severidade (para anomalias) */}
              {selectedAnalysisType === 'anomaly' && (
                <div className="mb-3 sm:mb-4">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">Severidade</label>
                  <select
                    value={selectedSeverity}
                    onChange={(e) => setSelectedSeverity(e.target.value)}
                    className="w-full sm:w-64 px-2 sm:px-3 py-1 sm:py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    {severityLevels.map(level => (
                      <option key={level.id} value={level.id}>{level.name}</option>
                    ))}
                  </select>
                </div>
              )}

              {/* Botões de Ação */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-200 space-y-3 sm:space-y-0">
                <div className="text-xs sm:text-sm text-gray-600">
                  Filtros aplicados: {selectedFarm !== 'all' ? '1' : '0'} fazenda, {selectedCrop !== 'all' ? '1' : '0'} cultura, {selectedDataSource !== 'all' ? '1' : '0'} fonte
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <button
                    onClick={() => {
                      setSelectedFarm('all');
                      setSelectedCrop('all');
                      setSelectedDataSource('all');
                      setSelectedSeverity('all');
                      setSelectedMetric('ndvi');
                      setSelectedAnalysisType('trend');
                    }}
                    className="px-3 sm:px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-sm"
                  >
                    Limpar Filtros
                  </button>
                  <button className="bg-emerald-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-1 sm:space-x-2 text-sm">
                    <RefreshCw className="w-3 sm:w-4 h-3 sm:h-4" />
                    <span>Aplicar</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Resumo dos Filtros Aplicados */}
        {(selectedFarm !== 'all' || selectedCrop !== 'all' || selectedDataSource !== 'all' || selectedSeverity !== 'all') && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Settings className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-blue-900">Filtros Aplicados:</span>
              </div>
              <button
                onClick={() => setShowFilters(true)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Editar Filtros
              </button>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {selectedFarm !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <MapPin className="w-3 h-3 mr-1" />
                  {farms.find(f => f.id === selectedFarm)?.name}
                </span>
              )}
              {selectedCrop !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <Layers className="w-3 h-3 mr-1" />
                  {crops.find(c => c.id === selectedCrop)?.name}
                </span>
              )}
              {selectedDataSource !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  <Eye className="w-3 h-3 mr-1" />
                  {dataSources.find(d => d.id === selectedDataSource)?.name}
                </span>
              )}
              {selectedSeverity !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <Target className="w-3 h-3 mr-1" />
                  Severidade: {severityLevels.find(s => s.id === selectedSeverity)?.name}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Key Insights */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
          {insights.map((insight, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-2 sm:mb-3">
                <div className={`w-2 sm:w-3 h-2 sm:h-3 rounded-full flex-shrink-0 ${
                  insight.type === 'positive' ? 'bg-green-500' :
                  insight.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                }`} />
                <span className={`text-xs px-1 sm:px-2 py-1 rounded-full flex-shrink-0 ${
                  insight.impact === 'Alto' ? 'bg-red-100 text-red-700' :
                  insight.impact === 'Médio' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {insight.impact}
                </span>
              </div>
              <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-1 sm:mb-2 line-clamp-2">{insight.title}</h3>
              <p className="text-xs sm:text-sm text-gray-600 line-clamp-3">{insight.description}</p>

              {/* Indicador de fonte de dados */}
              <div className="mt-2 sm:mt-3 pt-2 sm:pt-3 border-t border-gray-100">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs text-gray-500 space-y-1 sm:space-y-0">
                  <span className="truncate">Baseado em: {selectedDataSource === 'all' ? 'Todas as fontes' : dataSources.find(d => d.id === selectedDataSource)?.name}</span>
                  <span className="flex-shrink-0">{selectedPeriod === '6months' ? '6 meses' : selectedPeriod === '1year' ? '1 ano' : '3 meses'}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* NDVI Trend Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {selectedAnalysisType === 'trend' ? 'Tendência' :
                   selectedAnalysisType === 'correlation' ? 'Correlação' :
                   selectedAnalysisType === 'anomaly' ? 'Anomalias' : 'Produtividade'} - {
                   selectedMetric === 'ndvi' ? 'NDVI' :
                   selectedMetric === 'productivity' ? 'Produtividade' :
                   selectedMetric === 'rainfall' ? 'Precipitação' :
                   selectedMetric === 'temperature' ? 'Temperatura' :
                   selectedMetric === 'humidity' ? 'Umidade' : 'Umidade do Solo'
                  }
                </h3>
                <p className="text-sm text-gray-600">
                  {selectedFarm !== 'all' ? farms.find(f => f.id === selectedFarm)?.name : 'Todas as fazendas'} -
                  {selectedCrop !== 'all' ? crops.find(c => c.id === selectedCrop)?.name : 'Todas as culturas'}
                </p>
              </div>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <RefreshCw className="w-4 h-4" />
              </button>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={ndviTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis yAxisId="left" domain={[0, 1]} />
                <YAxis yAxisId="right" orientation="right" domain={[80, 100]} />
                <Tooltip />
                <Area yAxisId="left" type="monotone" dataKey="ndvi" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Productivity Comparison */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Comparação de {selectedMetric === 'productivity' ? 'Produtividade' : 'Métricas'} por Fazenda
              </h3>
              <p className="text-sm text-gray-600">
                Período: {selectedPeriod === '6months' ? 'Últimos 6 meses' :
                         selectedPeriod === '1year' ? 'Último ano' :
                         selectedPeriod === '3months' ? 'Últimos 3 meses' : 'Último mês'} -
                Fonte: {selectedDataSource === 'all' ? 'Todas as fontes' : dataSources.find(d => d.id === selectedDataSource)?.name}
              </p>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={productivityData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="farm" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="soja" fill="#10b981" name="Soja (kg/ha)" />
                <Bar dataKey="milho" fill="#f59e0b" name="Milho (kg/ha)" />
                <Bar dataKey="algodao" fill="#3b82f6" name="Algodão (kg/ha)" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Correlation Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Correlação NDVI x Produtividade</h3>
            <ResponsiveContainer width="100%" height={300}>
              <ScatterChart data={correlationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="ndvi" name="NDVI" />
                <YAxis dataKey="productivity" name="Produtividade" />
                <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                <Scatter dataKey="productivity" fill="#10b981" />
              </ScatterChart>
            </ResponsiveContainer>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Coeficiente de Correlação:</strong> 0.87 (Forte correlação positiva)
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Aumento no NDVI está fortemente associado ao aumento na produtividade
              </p>
            </div>
          </div>

          {/* Anomaly Detection */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Detecção de Anomalias</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={anomalyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="anomalies" fill="#ef4444" name="Anomalias Detectadas" />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Total de Anomalias:</span>
                <span className="font-medium text-gray-900">18</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Severidade Média:</span>
                <span className="font-medium text-yellow-600">Média</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Tendência:</span>
                <span className="font-medium text-green-600">Decrescente</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
