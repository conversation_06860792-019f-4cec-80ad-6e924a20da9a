import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, MapPin, User, Crop, Save, X, Search, AlertCircle } from 'lucide-react';
import { useFarms } from '../hooks/useFarms';
import type { TablesInsert } from '../lib/database.types';

type FarmInsert = Omit<TablesInsert<'farms'>, 'user_id'>;

const FarmManagement: React.FC = () => {
  const { farms, loading, error, createFarm, updateFarm, deleteFarm } = useFarms();
  const [selectedFarm, setSelectedFarm] = useState<any>(null);
  const [showFarmForm, setShowFarmForm] = useState(false);
  const [editingFarm, setEditingFarm] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const [farmForm, setFarmForm] = useState<FarmInsert>({
    name: '',
    address: '',
    latitude: 0,
    longitude: 0,
    total_area: 0,
    crop_type: '',
    owner_name: '',
    owner_contact: ''
  });

  const cropTypes = [
    'Soja', 'Milho', 'Algodão', 'Cana-de-açúcar', 'Café', 'Arroz', 
    'Feijão', 'Trigo', 'Sorgo', 'Girassol', 'Pastagem', 'Outros'
  ];

  const handleCreateFarm = () => {
    setEditingFarm(null);
    setFarmForm({
      name: '',
      address: '',
      latitude: 0,
      longitude: 0,
      total_area: 0,
      crop_type: '',
      owner_name: '',
      owner_contact: ''
    });
    setFormError(null);
    setShowFarmForm(true);
  };

  const handleEditFarm = (farm: any) => {
    setEditingFarm(farm);
    setFarmForm({
      name: farm.name,
      address: farm.address,
      latitude: farm.latitude,
      longitude: farm.longitude,
      total_area: farm.total_area,
      crop_type: farm.crop_type,
      owner_name: farm.owner_name,
      owner_contact: farm.owner_contact || ''
    });
    setFormError(null);
    setShowFarmForm(true);
  };

  const handleSaveFarm = async () => {
    setFormLoading(true);
    setFormError(null);
    
    try {
      if (editingFarm) {
        const { error } = await updateFarm(editingFarm.id, farmForm);
        if (error) throw new Error(error);
      } else {
        const { error } = await createFarm(farmForm);
        if (error) throw new Error(error);
      }
      
      setShowFarmForm(false);
      setEditingFarm(null);
    } catch (err) {
      setFormError(err instanceof Error ? err.message : 'Erro ao salvar fazenda');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteFarm = async (farmId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta fazenda?')) {
      const { error } = await deleteFarm(farmId);
      if (error) {
        alert(`Erro ao deletar fazenda: ${error}`);
      } else {
        if (selectedFarm?.id === farmId) {
          setSelectedFarm(null);
        }
      }
    }
  };

  const filteredFarms = farms.filter(farm =>
    farm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    farm.owner_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    farm.crop_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-emerald-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Carregando fazendas...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestão de Fazendas</h1>
          <p className="text-gray-600">Cadastre e gerencie suas propriedades rurais e áreas de plantio</p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Lista de Fazendas */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Fazendas Cadastradas</h2>
                  <button
                    onClick={handleCreateFarm}
                    className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Nova Fazenda</span>
                  </button>
                </div>
                
                {/* Busca */}
                <div className="relative">
                  <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Buscar fazendas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
              </div>

              <div className="divide-y divide-gray-200">
                {filteredFarms.length === 0 ? (
                  <div className="p-8 text-center">
                    <Crop className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {farms.length === 0 ? 'Nenhuma fazenda cadastrada' : 'Nenhuma fazenda encontrada'}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {farms.length === 0 
                        ? 'Comece cadastrando sua primeira propriedade rural'
                        : 'Tente ajustar os termos de busca'
                      }
                    </p>
                    {farms.length === 0 && (
                      <button
                        onClick={handleCreateFarm}
                        className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                      >
                        Cadastrar Primeira Fazenda
                      </button>
                    )}
                  </div>
                ) : (
                  filteredFarms.map((farm) => (
                    <div
                      key={farm.id}
                      className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                        selectedFarm?.id === farm.id ? 'bg-emerald-50 border-l-4 border-emerald-500' : ''
                      }`}
                      onClick={() => setSelectedFarm(farm)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">{farm.name}</h3>
                          <div className="space-y-2 text-sm text-gray-600">
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4" />
                              <span>{farm.address}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <User className="w-4 h-4" />
                              <span>{farm.owner_name}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Crop className="w-4 h-4" />
                              <span>{farm.crop_type} • {farm.total_area} ha</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-4 mt-3">
                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                              Criada em {new Date(farm.created_at).toLocaleDateString('pt-BR')}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-4">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditFarm(farm);
                            }}
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteFarm(farm.id);
                            }}
                            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Detalhes da Fazenda Selecionada */}
          <div className="space-y-6">
            {selectedFarm ? (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes da Fazenda</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Nome</label>
                    <p className="text-gray-900">{selectedFarm.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Proprietário</label>
                    <p className="text-gray-900">{selectedFarm.owner_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Área Total</label>
                    <p className="text-gray-900">{selectedFarm.total_area} hectares</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Cultivo Principal</label>
                    <p className="text-gray-900">{selectedFarm.crop_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Coordenadas</label>
                    <p className="text-gray-900 text-sm">
                      {selectedFarm.latitude.toFixed(6)}, {selectedFarm.longitude.toFixed(6)}
                    </p>
                  </div>
                  {selectedFarm.owner_contact && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Contato</label>
                      <p className="text-gray-900">{selectedFarm.owner_contact}</p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <p className="text-gray-500 text-center">Selecione uma fazenda para ver os detalhes</p>
              </div>
            )}
          </div>
        </div>

        {/* Modal de Formulário de Fazenda */}
        {showFarmForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {editingFarm ? 'Editar Fazenda' : 'Nova Fazenda'}
                  </h3>
                  <button
                    onClick={() => setShowFarmForm(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {formError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center space-x-2">
                    <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                    <span className="text-sm text-red-700">{formError}</span>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nome da Fazenda</label>
                    <input
                      type="text"
                      value={farmForm.name}
                      onChange={(e) => setFarmForm({ ...farmForm, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="Ex: Fazenda São João"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Proprietário</label>
                    <input
                      type="text"
                      value={farmForm.owner_name}
                      onChange={(e) => setFarmForm({ ...farmForm, owner_name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="Nome do proprietário"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
                  <input
                    type="text"
                    value={farmForm.address}
                    onChange={(e) => setFarmForm({ ...farmForm, address: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="Endereço completo da propriedade"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
                    <input
                      type="number"
                      step="any"
                      value={farmForm.latitude}
                      onChange={(e) => setFarmForm({ ...farmForm, latitude: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="-12.5489"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
                    <input
                      type="number"
                      step="any"
                      value={farmForm.longitude}
                      onChange={(e) => setFarmForm({ ...farmForm, longitude: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="-55.7183"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Área Total (ha)</label>
                    <input
                      type="number"
                      value={farmForm.total_area}
                      onChange={(e) => setFarmForm({ ...farmForm, total_area: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="2450"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                    <select
                      value={farmForm.crop_type}
                      onChange={(e) => setFarmForm({ ...farmForm, crop_type: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      required
                    >
                      <option value="">Selecione o cultivo</option>
                      {cropTypes.map((crop) => (
                        <option key={crop} value={crop}>{crop}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Contato (opcional)</label>
                    <input
                      type="text"
                      value={farmForm.owner_contact}
                      onChange={(e) => setFarmForm({ ...farmForm, owner_contact: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="(65) 99999-9999"
                    />
                  </div>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setShowFarmForm(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  disabled={formLoading}
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveFarm}
                  disabled={formLoading}
                  className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  <span>{formLoading ? 'Salvando...' : 'Salvar'}</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FarmManagement;