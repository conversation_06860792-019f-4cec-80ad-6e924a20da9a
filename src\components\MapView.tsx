import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Layers, Satellite, Navigation, ZoomIn, ZoomOut, RotateCcw, Filter, <PERSON>hair, AlertCircle, Camera, Eye, RefreshCw, Target, Image } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, useMap, useMapEvents, ImageOverlay } from 'react-leaflet';
import L from '../leaflet-setup';
import 'leaflet/dist/leaflet.css';
import LocationInfo from './LocationInfo';

// Interface para imagens de drone
interface DroneImage {
  id: number;
  filename: string;
  latitude: number;
  longitude: number;
  altitude: number;
  capture_date: string;
  bounds: [[number, number], [number, number]]; // [[lat_min, lng_min], [lat_max, lng_max]]
  imageUrl: string;
  ndvi_data?: {
    mean: number;
    min: number;
    max: number;
  };
  analysis_results?: {
    vegetation_health: 'healthy' | 'stressed' | 'critical';
    pest_detection: boolean;
    disease_detection: boolean;
  };
}

// Ícones customizados para diferentes status
const createCustomIcon = (color: string) => {
  return L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  });
};

const farmIcons = {
  healthy: createCustomIcon('#10b981'),
  warning: createCustomIcon('#f59e0b'),
  critical: createCustomIcon('#ef4444'),
  user: L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: #3b82f6; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [16, 16],
    iconAnchor: [8, 8]
  }),
  drone: L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color: #8b5cf6; width: 24px; height: 24px; border-radius: 4px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center;"><span style="color: white; font-size: 12px;">📷</span></div>`,
    iconSize: [24, 24],
    iconAnchor: [12, 12]
  })
};

// Componente para localização do usuário
const LocationMarker: React.FC<{ position: [number, number] | null }> = ({ position }) => {
  const map = useMap();

  // Atualizar o centro do mapa quando a posição mudar
  useEffect(() => {
    if (position) {
      console.log('Atualizando mapa para posição:', position);
      map.setView(position, map.getZoom());
    }
  }, [position, map]);

  useMapEvents({
    locationfound(e) {
      console.log('Evento locationfound:', e.latlng);
      map.flyTo(e.latlng, map.getZoom());
    },
  });

  return position === null ? null : (
    <Marker position={position} icon={farmIcons.user}>
      <Popup>
        <div className="text-center">
          <strong>📍 Sua localização atual</strong>
          <br />
          <small>
            {position[0].toFixed(6)}, {position[1].toFixed(6)}
          </small>
        </div>
      </Popup>
    </Marker>
  );
};

// Componente para controles do mapa
const MapControls: React.FC<{ onLocate: () => void }> = ({ onLocate }) => {
  const map = useMap();

  return (
    <div className="leaflet-top leaflet-right">
      <div className="leaflet-control leaflet-bar">
        <button
          className="leaflet-control-button"
          onClick={() => map.zoomIn()}
          title="Zoom In"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          +
        </button>
        <button
          className="leaflet-control-button"
          onClick={() => map.zoomOut()}
          title="Zoom Out"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          -
        </button>
        <button
          className="leaflet-control-button"
          onClick={onLocate}
          title="Minha Localização"
          style={{ width: '30px', height: '30px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          📍
        </button>
      </div>
    </div>
  );
};

// Componente para atualizar o centro do mapa
const MapUpdater: React.FC<{ center: [number, number]; zoom: number }> = ({ center, zoom }) => {
  const map = useMap();

  useEffect(() => {
    console.log('MapUpdater: Atualizando mapa para:', center, 'zoom:', zoom);
    map.setView(center, zoom);
  }, [center, zoom, map]);

  return null;
};

const MapView: React.FC = () => {
  const [selectedLayer, setSelectedLayer] = useState('hybrid');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-12.5489, -55.7183]);
  const [mapZoom, setMapZoom] = useState(10);
  const [droneImages, setDroneImages] = useState<DroneImage[]>([]);
  const [selectedDroneImage, setSelectedDroneImage] = useState<number | null>(null);
  const [showDroneOverlays, setShowDroneOverlays] = useState(true);
  const [isTrackingLocation, setIsTrackingLocation] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);
  const mapRef = useRef<L.Map | null>(null);

  // TODO: Replace with actual farms data from API
  const farms: any[] = [];

  // Load drone images from API
  useEffect(() => {
    // TODO: Implement API call to fetch drone images
    setDroneImages([]);
  }, []);

  // Função para obter localização GPS
  const getCurrentLocation = () => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    // Primeiro, tentar com alta precisão
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];

        console.log('Localização obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`,
          timestamp: new Date(position.timestamp).toLocaleString()
        });

        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(16);

        // Mostrar mensagem de sucesso temporária
        setLocationError(`Localização obtida com precisão de ${accuracy.toFixed(0)}m`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização:', error);
        let errorMessage = 'Erro ao obter localização';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada. Permita o acesso à localização nas configurações do navegador.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível. Verifique se o GPS está ativado.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Timeout ao obter localização. Tentando novamente com menor precisão...';
            // Tentar novamente com configurações menos restritivas
            fallbackLocation();
            return;
        }
        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000
      }
    );
  };

  // Função de fallback para localização menos precisa
  const fallbackLocation = () => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];

        console.log('Localização fallback obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`
        });

        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(14);

        setLocationError(`Localização aproximada obtida (${accuracy.toFixed(0)}m)`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização fallback:', error);
        setLocationError('Não foi possível obter sua localização. Usando localização padrão.');

        // Usar localização padrão (centro do Brasil)
        const defaultLocation: [number, number] = [-15.7942, -47.8822]; // Brasília
        setUserLocation(defaultLocation);
        setMapCenter(defaultLocation);
        setMapZoom(12);
      },
      {
        enableHighAccuracy: false,
        timeout: 10000,
        maximumAge: 300000 // 5 minutos
      }
    );
  };

  // Função para iniciar/parar rastreamento contínuo de localização
  const toggleLocationTracking = () => {
    if (isTrackingLocation) {
      // Parar rastreamento
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        setWatchId(null);
      }
      setIsTrackingLocation(false);
    } else {
      // Iniciar rastreamento
      if (!navigator.geolocation) {
        setLocationError('Geolocalização não é suportada neste navegador');
        return;
      }

      const id = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newLocation: [number, number] = [latitude, longitude];
          setUserLocation(newLocation);
          setLocationError(null);
        },
        (error) => {
          let errorMessage = 'Erro no rastreamento';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Permissão de localização negada';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Localização indisponível';
              break;
            case error.TIMEOUT:
              errorMessage = 'Timeout no rastreamento';
              break;
          }
          setLocationError(errorMessage);
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 1000
        }
      );

      setWatchId(id);
      setIsTrackingLocation(true);
    }
  };

  // Função para sincronizar com imagem de drone mais próxima
  const syncWithNearestDroneImage = () => {
    if (!userLocation || droneImages.length === 0) return;

    let nearestImage = droneImages[0];
    let minDistance = calculateDistance(
      userLocation[0], userLocation[1],
      nearestImage.latitude, nearestImage.longitude
    );

    droneImages.forEach(image => {
      const distance = calculateDistance(
        userLocation[0], userLocation[1],
        image.latitude, image.longitude
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestImage = image;
      }
    });

    // Centralizar no local da imagem mais próxima
    setMapCenter([nearestImage.latitude, nearestImage.longitude]);
    setMapZoom(18);
    setSelectedDroneImage(nearestImage.id);
  };

  // Função para calcular distância entre dois pontos
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Raio da Terra em km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Obter localização automaticamente ao carregar o componente (apenas se não houver erro)
  useEffect(() => {
    // Verificar se o navegador suporta geolocalização antes de tentar
    if (navigator.geolocation) {
      // Usar um timeout para não bloquear a interface
      const timer = setTimeout(() => {
        getCurrentLocation();
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setLocationError('Geolocalização não é suportada neste navegador');
    }
  }, []);

  // Limpar watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const layers = [
    { id: 'hybrid', name: 'Híbrido', icon: Layers },
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'ndvi', name: 'NDVI', icon: Target },
    { id: 'terrain', name: 'Terreno', icon: MapPin },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo</h1>
          <p className="text-gray-600">Visualização geoespacial das propriedades e análises espectrais</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => setSelectedLayer(layer.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                    className={`p-2 rounded-lg transition-colors ${
                      showDroneOverlays
                        ? 'text-purple-600 bg-purple-100 hover:bg-purple-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Mostrar/Ocultar imagens de drone"
                  >
                    <Camera className="w-5 h-5" />
                  </button>
                  <button
                    onClick={syncWithNearestDroneImage}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Sincronizar com imagem de drone mais próxima"
                  >
                    <RefreshCw className="w-5 h-5" />
                  </button>
                  <button
                    onClick={toggleLocationTracking}
                    className={`p-2 rounded-lg transition-colors ${
                      isTrackingLocation
                        ? 'text-green-600 bg-green-100 hover:bg-green-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title={isTrackingLocation ? "Parar rastreamento" : "Iniciar rastreamento GPS"}
                  >
                    <Target className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Filtros"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button
                    onClick={getCurrentLocation}
                    className={`p-2 rounded-lg transition-colors ${
                      userLocation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Minha localização"
                  >
                    <Navigation className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      setMapCenter([-12.5489, -55.7183]);
                      setMapZoom(10);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Centralizar no Brasil"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Mapa Interativo Real */}
              <div className="relative h-96">
                {locationError && (
                  <div className={`absolute top-2 left-2 z-[1000] px-3 py-2 rounded-lg text-sm flex items-center space-x-2 ${
                    locationError.includes('obtida')
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <AlertCircle className="w-4 h-4" />
                    <span>{locationError}</span>
                  </div>
                )}

                {/* Debug Info */}
                {userLocation && (
                  <div className="absolute top-2 right-2 z-[1000] bg-blue-100 border border-blue-400 text-blue-700 px-3 py-2 rounded-lg text-xs">
                    <div><strong>GPS:</strong> {userLocation[0].toFixed(6)}, {userLocation[1].toFixed(6)}</div>
                    <div><strong>Zoom:</strong> {mapZoom}</div>
                    {isTrackingLocation && <div className="text-green-600"><strong>🎯 Rastreando</strong></div>}
                  </div>
                )}

                <MapContainer
                  center={mapCenter}
                  zoom={mapZoom}
                  style={{ height: '100%', width: '100%' }}
                  ref={mapRef}
                >
                  {/* Componente para atualizar o mapa */}
                  <MapUpdater center={mapCenter} zoom={mapZoom} />
                  {/* Camadas de Mapa */}
                  {(selectedLayer === 'hybrid' || selectedLayer === 'satellite') && (
                    <TileLayer
                      url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                      attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
                    />
                  )}
                  {(selectedLayer === 'hybrid') && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.3}
                    />
                  )}
                  {selectedLayer === 'terrain' && (
                    <TileLayer
                      url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://opentopomap.org/">OpenTopoMap</a>'
                    />
                  )}
                  {selectedLayer === 'ndvi' && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.7}
                    />
                  )}

                  {/* Overlays de Imagens de Drone */}
                  {showDroneOverlays && droneImages.map((image) => (
                    <ImageOverlay
                      key={image.id}
                      url={image.imageUrl}
                      bounds={image.bounds}
                      opacity={selectedDroneImage === image.id ? 0.8 : 0.6}
                    />
                  ))}

                  {/* Marcadores das Fazendas */}
                  {farms.map((farm) => (
                    <Marker
                      key={farm.id}
                      position={[farm.coordinates.lat, farm.coordinates.lng]}
                      icon={farmIcons[farm.status as keyof typeof farmIcons]}
                      eventHandlers={{
                        click: () => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)
                      }}
                    >
                      <Popup>
                        <div className="p-2">
                          <h3 className="font-semibold text-gray-900">{farm.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{farm.description}</p>
                          <div className="space-y-1 text-xs">
                            <div><strong>Proprietário:</strong> {farm.owner}</div>
                            <div><strong>Área:</strong> {farm.area} ha</div>
                            <div><strong>NDVI:</strong> {farm.ndvi}</div>
                            <div><strong>Status:</strong> {getStatusLabel(farm.status)}</div>
                            {farm.alerts > 0 && (
                              <div className="text-yellow-600"><strong>Alertas:</strong> {farm.alerts}</div>
                            )}
                            {userLocation && (
                              <div><strong>Distância:</strong> {
                                calculateDistance(
                                  userLocation[0], userLocation[1],
                                  farm.coordinates.lat, farm.coordinates.lng
                                ).toFixed(1)
                              } km</div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {/* Marcador da localização do usuário */}
                  {userLocation && (
                    <Marker
                      position={userLocation}
                      icon={userLocationIcon}
                    >
                      <Popup>
                        <div className="p-2">
                          <h3 className="font-semibold text-blue-900">Sua Localização</h3>
                          <div className="text-xs space-y-1">
                            <div><strong>Latitude:</strong> {userLocation[0].toFixed(6)}</div>
                            <div><strong>Longitude:</strong> {userLocation[1].toFixed(6)}</div>
                            {isTrackingLocation && (
                              <div className="text-green-600"><strong>🎯 Rastreamento ativo</strong></div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  )}

                  {/* Marcadores das imagens de drone */}
                  {droneImages.map((image) => (
                    <Marker
                      key={`drone-${image.id}`}
                      position={[image.latitude, image.longitude]}
                      icon={droneIcon}
                      eventHandlers={{
                        click: () => setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id)
                      }}
                    >
                      <Popup>
                        <div className="p-2">
                          <h3 className="font-semibold text-purple-900">Imagem de Drone</h3>
                          <p className="text-sm text-gray-600 mb-2">{image.filename}</p>
                          <div className="space-y-1 text-xs">
                            <div><strong>Altitude:</strong> {image.altitude}m</div>
                            <div><strong>Data:</strong> {new Date(image.capture_date).toLocaleDateString()}</div>
                            {image.ndvi_data && (
                              <div><strong>NDVI Médio:</strong> {image.ndvi_data.mean}</div>
                            )}
                            {image.analysis_results && (
                              <div><strong>Saúde:</strong> {image.analysis_results.vegetation_health}</div>
                            )}
                            {userLocation && (
                              <div><strong>Distância:</strong> {
                                calculateDistance(
                                  userLocation[0], userLocation[1],
                                  image.latitude, image.longitude
                                ).toFixed(1)
                              } km</div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}
                </MapContainer>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Filtros */}
            {showFilters && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Filtros</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status da Fazenda</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                      <option value="">Todos</option>
                      <option value="healthy">Saudável</option>
                      <option value="warning">Atenção</option>
                      <option value="critical">Crítico</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">NDVI Mínimo</label>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Lista de Fazendas */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedFarm(selectedFarm === farm.id ? null : farm.id);
                      setMapCenter([farm.coordinates.lat, farm.coordinates.lng]);
                      setMapZoom(14);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <span className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`}></span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{farm.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{farm.area} ha</span>
                      <span>NDVI: {farm.ndvi}</span>
                    </div>
                    {farm.alerts > 0 && (
                      <div className="mt-2 text-xs text-yellow-600">
                        {farm.alerts} alerta{farm.alerts > 1 ? 's' : ''}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Lista de Imagens de Drone */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Imagens de Drone</h3>
              <div className="space-y-3">
                {droneImages.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedDroneImage === image.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
                      setMapCenter([image.latitude, image.longitude]);
                      setMapZoom(18);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{image.filename}</h4>
                      <Camera className="w-4 h-4 text-purple-600" />
                    </div>
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>Altitude: {image.altitude}m</div>
                      <div>Data: {new Date(image.capture_date).toLocaleDateString()}</div>
                      {image.ndvi_data && (
                        <div>NDVI: {image.ndvi_data.mean}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
      id: 1,
      filename: 'drone_001_rgb.jpg',
      latitude: -12.5489,
      longitude: -55.7183,
      altitude: 120,
      capture_date: '2024-06-20T10:30:00',
      bounds: [[-12.5495, -55.7190], [-12.5483, -55.7176]],
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzEwYjk4MSIgb3BhY2l0eT0iMC43Ii8+PHRleHQgeD0iNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSI+TkRWSSAwLjc1PC90ZXh0Pjwvc3ZnPg==',
      ndvi_data: {
        mean: 0.75,
        min: 0.45,
        max: 0.89
      },
      analysis_results: {
        vegetation_health: 'healthy',
        pest_detection: false,
        disease_detection: false
      }
    },
    {
      id: 2,
      filename: 'drone_002_nir.jpg',
      latitude: -12.5495,
      longitude: -55.7190,
      altitude: 100,
      capture_date: '2024-06-21T09:15:00',
      bounds: [[-12.5501, -55.7197], [-12.5489, -55.7183]],
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1OWUwYiIgb3BhY2l0eT0iMC43Ii8+PHRleHQgeD0iNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSI+TkRWSSAwLjYyPC90ZXh0Pjwvc3ZnPg==',
      ndvi_data: {
        mean: 0.62,
        min: 0.38,
        max: 0.78
      },
      analysis_results: {
        vegetation_health: 'stressed',
        pest_detection: true,
        disease_detection: false
      }
    },
    {
      id: 3,
      filename: 'drone_003_multi.jpg',
      latitude: -13.2500,
      longitude: -56.1000,
      altitude: 150,
      capture_date: '2024-06-22T14:45:00',
      bounds: [[-13.2506, -56.1007], [-13.2494, -56.0993]],
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzNiODJmNiIgb3BhY2l0eT0iMC43Ii8+PHRleHQgeD0iNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSI+TkRWSSAwLjgyPC90ZXh0Pjwvc3ZnPg==',
      ndvi_data: {
        mean: 0.82,
        min: 0.65,
        max: 0.95
      },
      analysis_results: {
        vegetation_health: 'healthy',
        pest_detection: false,
        disease_detection: false
      }
    }
  ];

  // Carregar imagens de drone
  useEffect(() => {
    setDroneImages(mockDroneImages);
  }, []);

  // Função para obter localização GPS
  const getCurrentLocation = () => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    // Primeiro, tentar com alta precisão
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];

        console.log('Localização obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`,
          timestamp: new Date(position.timestamp).toLocaleString()
        });

        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(16);

        // Mostrar mensagem de sucesso temporária
        setLocationError(`Localização obtida com precisão de ${accuracy.toFixed(0)}m`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização:', error);
        let errorMessage = 'Erro ao obter localização';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada. Permita o acesso à localização nas configurações do navegador.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível. Verifique se o GPS está ativado.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Timeout ao obter localização. Tentando novamente com menor precisão...';
            // Tentar novamente com configurações menos restritivas
            fallbackLocation();
            return;
        }
        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 30000
      }
    );
  };

  // Função de fallback para localização menos precisa
  const fallbackLocation = () => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];

        console.log('Localização fallback obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`
        });

        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(14);

        setLocationError(`Localização aproximada obtida (${accuracy.toFixed(0)}m)`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização fallback:', error);
        setLocationError('Não foi possível obter sua localização. Usando localização padrão.');

        // Usar localização padrão (centro do Brasil)
        const defaultLocation: [number, number] = [-15.7942, -47.8822]; // Brasília
        setUserLocation(defaultLocation);
        setMapCenter(defaultLocation);
        setMapZoom(12);
      },
      {
        enableHighAccuracy: false,
        timeout: 10000,
        maximumAge: 300000 // 5 minutos
      }
    );
  };

  // Função para iniciar/parar rastreamento contínuo de localização
  const toggleLocationTracking = () => {
    if (isTrackingLocation) {
      // Parar rastreamento
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        setWatchId(null);
      }
      setIsTrackingLocation(false);
    } else {
      // Iniciar rastreamento
      if (!navigator.geolocation) {
        setLocationError('Geolocalização não é suportada neste navegador');
        return;
      }

      const id = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newLocation: [number, number] = [latitude, longitude];
          setUserLocation(newLocation);
          setLocationError(null);
        },
        (error) => {
          let errorMessage = 'Erro no rastreamento';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Permissão de localização negada';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Localização indisponível';
              break;
            case error.TIMEOUT:
              errorMessage = 'Timeout no rastreamento';
              break;
          }
          setLocationError(errorMessage);
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 1000
        }
      );

      setWatchId(id);
      setIsTrackingLocation(true);
    }
  };

  // Função para sincronizar com imagem de drone mais próxima
  const syncWithNearestDroneImage = () => {
    if (!userLocation || droneImages.length === 0) return;

    let nearestImage = droneImages[0];
    let minDistance = calculateDistance(
      userLocation[0], userLocation[1],
      nearestImage.latitude, nearestImage.longitude
    );

    droneImages.forEach(image => {
      const distance = calculateDistance(
        userLocation[0], userLocation[1],
        image.latitude, image.longitude
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestImage = image;
      }
    });

    // Centralizar no local da imagem mais próxima
    setMapCenter([nearestImage.latitude, nearestImage.longitude]);
    setMapZoom(18);
    setSelectedDroneImage(nearestImage.id);
  };

  // Função para calcular distância entre dois pontos
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Raio da Terra em km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Obter localização automaticamente ao carregar o componente (apenas se não houver erro)
  useEffect(() => {
    // Verificar se o navegador suporta geolocalização antes de tentar
    if (navigator.geolocation) {
      // Usar um timeout para não bloquear a interface
      const timer = setTimeout(() => {
        getCurrentLocation();
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setLocationError('Geolocalização não é suportada neste navegador');
    }
  }, []);

  // Limpar watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const layers = [
    { id: 'hybrid', name: 'Híbrido', icon: Layers },
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'ndvi', name: 'NDVI', icon: Target },
    { id: 'terrain', name: 'Terreno', icon: MapPin },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo</h1>
          <p className="text-gray-600">Visualização geoespacial das propriedades e análises espectrais</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => setSelectedLayer(layer.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                    className={`p-2 rounded-lg transition-colors ${
                      showDroneOverlays
                        ? 'text-purple-600 bg-purple-100 hover:bg-purple-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Mostrar/Ocultar imagens de drone"
                  >
                    <Camera className="w-5 h-5" />
                  </button>
                  <button
                    onClick={syncWithNearestDroneImage}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Sincronizar com imagem de drone mais próxima"
                  >
                    <RefreshCw className="w-5 h-5" />
                  </button>
                  <button
                    onClick={toggleLocationTracking}
                    className={`p-2 rounded-lg transition-colors ${
                      isTrackingLocation
                        ? 'text-green-600 bg-green-100 hover:bg-green-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title={isTrackingLocation ? "Parar rastreamento" : "Iniciar rastreamento GPS"}
                  >
                    <Target className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Filtros"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button
                    onClick={getCurrentLocation}
                    className={`p-2 rounded-lg transition-colors ${
                      userLocation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Minha localização"
                  >
                    <Navigation className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      setMapCenter([-12.5489, -55.7183]);
                      setMapZoom(10);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Centralizar no Brasil"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Mapa Interativo Real */}
              <div className="relative h-96">
                {locationError && (
                  <div className={`absolute top-2 left-2 z-[1000] px-3 py-2 rounded-lg text-sm flex items-center space-x-2 ${
                    locationError.includes('obtida')
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <AlertCircle className="w-4 h-4" />
                    <span>{locationError}</span>
                  </div>
                )}

                {/* Debug Info */}
                {userLocation && (
                  <div className="absolute top-2 right-2 z-[1000] bg-blue-100 border border-blue-400 text-blue-700 px-3 py-2 rounded-lg text-xs">
                    <div><strong>GPS:</strong> {userLocation[0].toFixed(6)}, {userLocation[1].toFixed(6)}</div>
                    <div><strong>Zoom:</strong> {mapZoom}</div>
                    {isTrackingLocation && <div className="text-green-600"><strong>🎯 Rastreando</strong></div>}
                  </div>
                )}

                <MapContainer
                  center={mapCenter}
                  zoom={mapZoom}
                  style={{ height: '100%', width: '100%' }}
                  ref={mapRef}
                >
                  {/* Componente para atualizar o mapa */}
                  <MapUpdater center={mapCenter} zoom={mapZoom} />
                  {/* Camadas de Mapa */}
                  {(selectedLayer === 'hybrid' || selectedLayer === 'satellite') && (
                    <TileLayer
                      url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                      attribution='&copy; <a href="https://www.esri.com/">Esri</a>'
                    />
                  )}
                  {(selectedLayer === 'hybrid') && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.3}
                    />
                  )}
                  {selectedLayer === 'terrain' && (
                    <TileLayer
                      url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://opentopomap.org/">OpenTopoMap</a>'
                    />
                  )}
                  {selectedLayer === 'ndvi' && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.7}
                    />
                  )}

                  {/* Overlays de Imagens de Drone */}
                  {showDroneOverlays && droneImages.map((image) => (
                    <ImageOverlay
                      key={image.id}
                      url={image.imageUrl}
                      bounds={image.bounds}
                      opacity={selectedDroneImage === image.id ? 0.8 : 0.6}
                    />
                  ))}

                  {/* Marcadores das Fazendas */}
                  {farms.map((farm) => (
                    <Marker
                      key={farm.id}
                      position={[farm.coordinates.lat, farm.coordinates.lng]}
                      icon={farmIcons[farm.status as keyof typeof farmIcons]}
                      eventHandlers={{
                        click: () => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)
                      }}
                    >
                      <Popup>
                        <div className="p-2">
                          <h3 className="font-semibold text-gray-900">{farm.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{farm.description}</p>
                          <div className="space-y-1 text-xs">
                            <div><strong>Proprietário:</strong> {farm.owner}</div>
                            <div><strong>Área:</strong> {farm.area} ha</div>
                            <div><strong>NDVI:</strong> {farm.ndvi}</div>
                            <div><strong>Status:</strong> {getStatusLabel(farm.status)}</div>
                            {farm.alerts > 0 && (
                              <div className="text-yellow-600"><strong>Alertas:</strong> {farm.alerts}</div>
                            )}
                            {userLocation && (
                              <div><strong>Distância:</strong> {
                                calculateDistance(
                                  userLocation[0], userLocation[1],
                                  farm.coordinates.lat, farm.coordinates.lng
                                ).toFixed(1)
                              } km</div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {/* Marcadores das Imagens de Drone */}
                  {droneImages.map((image) => (
                    <Marker
                      key={`drone-${image.id}`}
                      position={[image.latitude, image.longitude]}
                      icon={farmIcons.drone}
                      eventHandlers={{
                        click: () => setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id)
                      }}
                    >
                      <Popup>
                        <div className="p-3 min-w-[250px]">
                          <h3 className="font-semibold text-gray-900 mb-2">📷 {image.filename}</h3>
                          <div className="space-y-2 text-xs">
                            <div><strong>Data:</strong> {new Date(image.capture_date).toLocaleString('pt-BR')}</div>
                            <div><strong>Altitude:</strong> {image.altitude}m</div>
                            <div><strong>Coordenadas:</strong> {image.latitude.toFixed(6)}, {image.longitude.toFixed(6)}</div>
                            {image.ndvi_data && (
                              <div className="bg-green-50 p-2 rounded">
                                <div><strong>NDVI Médio:</strong> {image.ndvi_data.mean.toFixed(2)}</div>
                                <div><strong>Faixa:</strong> {image.ndvi_data.min.toFixed(2)} - {image.ndvi_data.max.toFixed(2)}</div>
                              </div>
                            )}
                            {image.analysis_results && (
                              <div className="bg-blue-50 p-2 rounded">
                                <div><strong>Saúde:</strong> {
                                  image.analysis_results.vegetation_health === 'healthy' ? '🟢 Saudável' :
                                  image.analysis_results.vegetation_health === 'stressed' ? '🟡 Estressada' : '🔴 Crítica'
                                }</div>
                                <div><strong>Pragas:</strong> {image.analysis_results.pest_detection ? '⚠️ Detectadas' : '✅ Não detectadas'}</div>
                                <div><strong>Doenças:</strong> {image.analysis_results.disease_detection ? '⚠️ Detectadas' : '✅ Não detectadas'}</div>
                              </div>
                            )}
                            {userLocation && (
                              <div><strong>Distância:</strong> {
                                calculateDistance(
                                  userLocation[0], userLocation[1],
                                  image.latitude, image.longitude
                                ).toFixed(1)
                              } km da sua localização</div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {/* Marcador da Localização do Usuário */}
                  <LocationMarker position={userLocation} />

                  {/* Controles Customizados */}
                  <MapControls onLocate={getCurrentLocation} />
                </MapContainer>

                {/* Botão de Localização */}
                <button
                  onClick={getCurrentLocation}
                  className="absolute bottom-4 right-4 z-[1000] bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Obter minha localização"
                >
                  <Crosshair className="w-5 h-5" />
                </button>

                {/* Legend */}
                <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg p-3">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Legenda</h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span className="text-xs text-gray-600">Fazenda Saudável</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span className="text-xs text-gray-600">Fazenda Atenção</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span className="text-xs text-gray-600">Fazenda Crítica</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span className="text-xs text-gray-600">Sua localização</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded bg-purple-500" />
                      <span className="text-xs text-gray-600">Imagem de drone</span>
                    </div>
                    {isTrackingLocation && (
                      <div className="flex items-center space-x-2 pt-1 border-t border-gray-200">
                        <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
                        <span className="text-xs text-green-600 font-medium">GPS Ativo</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Filters Panel */}
              {showFilters && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Location Info */}
            <LocationInfo
              position={userLocation}
              onLocationUpdate={(data) => {
                setUserLocation([data.latitude, data.longitude]);
              }}
            />

            {/* Drone Images List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Imagens de Drone</h3>
                <button
                  onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                  className={`text-xs px-2 py-1 rounded-full ${
                    showDroneOverlays
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {showDroneOverlays ? 'Visível' : 'Oculto'}
                </button>
              </div>
              <div className="space-y-3">
                {droneImages.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedDroneImage === image.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
                      setMapCenter([image.latitude, image.longitude]);
                      setMapZoom(18);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm">📷 {image.filename}</h4>
                      <span className="text-xs text-gray-500">
                        {new Date(image.capture_date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>Altitude: {image.altitude}m</p>
                      {image.ndvi_data && (
                        <p>NDVI: {image.ndvi_data.mean.toFixed(2)}</p>
                      )}
                      {image.analysis_results && (
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${
                            image.analysis_results.vegetation_health === 'healthy' ? 'bg-green-500' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                          <span>{
                            image.analysis_results.vegetation_health === 'healthy' ? 'Saudável' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'Estressada' : 'Crítica'
                          }</span>
                        </div>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {calculateDistance(
                            userLocation[0], userLocation[1],
                            image.latitude, image.longitude
                          ).toFixed(1)} km
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Farm List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Área: {farm.area} ha</p>
                      <p>NDVI: {farm.ndvi}</p>
                      <p>Status: {getStatusLabel(farm.status)}</p>
                      {farm.alerts > 0 && (
                        <p className="text-yellow-600">{farm.alerts} alerta(s)</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Farm Details */}
            {selectedFarm && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Detalhes</h3>
                {(() => {
                  const farm = farms.find(f => f.id === selectedFarm);
                  if (!farm) return null;

                  return (
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Coordenadas</label>
                        <p className="text-sm text-gray-900">
                          {farm.coordinates.lat.toFixed(6)}, {farm.coordinates.lng.toFixed(6)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Área Total</label>
                        <p className="text-sm text-gray-900">{farm.area} hectares</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">NDVI Atual</label>
                        <p className="text-sm text-gray-900">{farm.ndvi}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status</label>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(farm.status)}`} />
                          <span className="text-sm text-gray-900">{getStatusLabel(farm.status)}</span>
                        </div>
                      </div>
                      {userLocation && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">Distância</label>
                          <p className="text-sm text-gray-900">
                            {calculateDistance(
                              userLocation[0], userLocation[1],
                              farm.coordinates.lat, farm.coordinates.lng
                            ).toFixed(1)} km da sua localização
                          </p>
                        </div>
                      )}
                      <div>
                        <label className="text-sm font-medium text-gray-700">Proprietário</label>
                        <p className="text-sm text-gray-900">{farm.owner}</p>
                      </div>
                      {farm.alerts > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">Alertas</label>
                          <p className="text-sm text-yellow-600">{farm.alerts} alerta(s) ativo(s)</p>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;
