import React, { useState, useEffect, useRef, useCallback } from 'react';
import { MapPin, Layers, Satellite, Navigation, Filter, Crosshair, AlertCircle, Camera, RefreshCw, Target, RotateCcw, ZoomIn, ZoomOut } from 'lucide-react';
import LocationInfo from './LocationInfo';
import { useFarms } from '../hooks/useFarms';

// Interface para imagens de drone
interface DroneImage {
  id: number;
  filename: string;
  latitude: number;
  longitude: number;
  altitude: number;
  capture_date: string;
  ndvi_data?: {
    mean: number;
    min: number;
    max: number;
  };
  analysis_results?: {
    vegetation_health: 'healthy' | 'stressed' | 'critical';
    pest_detection: boolean;
    disease_detection: boolean;
  };
}

interface MapPoint {
  x: number;
  y: number;
  lat: number;
  lng: number;
  type: 'farm' | 'drone' | 'user';
  data: any;
}

const MapViewCustom: React.FC = () => {
  const { farms, loading: farmsLoading } = useFarms();
  const [selectedLayer, setSelectedLayer] = useState('hybrid');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-12.5489, -55.7183]);
  const [mapZoom, setMapZoom] = useState(6);
  const [droneImages, setDroneImages] = useState<DroneImage[]>([]);
  const [selectedDroneImage, setSelectedDroneImage] = useState<number | null>(null);
  const [showDroneOverlays, setShowDroneOverlays] = useState(true);
  const [isTrackingLocation, setIsTrackingLocation] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState<{x: number, y: number} | null>(null);
  const [selectedPoint, setSelectedPoint] = useState<MapPoint | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // TODO: Replace with actual drone images data from API
  // Load drone images from API
  useEffect(() => {
    // TODO: Implement API call to fetch drone images
    setDroneImages([]);
  }, []);

  // Função para converter coordenadas geográficas para pixels
  const latLngToPixel = useCallback((lat: number, lng: number, canvasWidth: number, canvasHeight: number) => {
    const scale = Math.pow(2, mapZoom);
    const worldWidth = 256 * scale;
    const worldHeight = 256 * scale;

    // Projeção Mercator simplificada
    const x = (lng + 180) * (worldWidth / 360);
    const latRad = lat * Math.PI / 180;
    const mercN = Math.log(Math.tan((Math.PI / 4) + (latRad / 2)));
    const y = (worldHeight / 2) - (worldWidth * mercN / (2 * Math.PI));

    // Centralizar no mapa
    const centerX = (mapCenter[1] + 180) * (worldWidth / 360);
    const centerLatRad = mapCenter[0] * Math.PI / 180;
    const centerMercN = Math.log(Math.tan((Math.PI / 4) + (centerLatRad / 2)));
    const centerY = (worldHeight / 2) - (worldWidth * centerMercN / (2 * Math.PI));

    return {
      x: x - centerX + canvasWidth / 2,
      y: y - centerY + canvasHeight / 2
    };
  }, [mapCenter, mapZoom]);

  // Função para desenhar o mapa
  const drawMap = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;

    // Limpar canvas
    ctx.clearRect(0, 0, width, height);

    // Desenhar fundo baseado na camada selecionada
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    switch (selectedLayer) {
      case 'satellite':
        gradient.addColorStop(0, '#2d5016');
        gradient.addColorStop(1, '#1a2e0a');
        break;
      case 'terrain':
        gradient.addColorStop(0, '#8b7355');
        gradient.addColorStop(1, '#5d4e37');
        break;
      case 'hybrid':
        gradient.addColorStop(0, '#2d5016');
        gradient.addColorStop(0.5, '#1a2e0a');
        gradient.addColorStop(1, '#0f1419');
        break;
      default:
        gradient.addColorStop(0, '#e5e7eb');
        gradient.addColorStop(1, '#d1d5db');
    }
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Desenhar grid se zoom alto
    if (mapZoom > 8) {
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;
      const gridSize = 50;
      for (let x = 0; x < width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      for (let y = 0; y < height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
    }

    // Desenhar marcadores das fazendas
    farms.forEach(farm => {
      if (farm.latitude && farm.longitude) {
        const point = latLngToPixel(farm.latitude, farm.longitude, width, height);
        if (point.x >= -20 && point.x <= width + 20 && point.y >= -20 && point.y <= height + 20) {
          // Cor baseada no status (sempre healthy por enquanto)
          let color = '#10b981'; // healthy

          // Desenhar círculo
          ctx.beginPath();
          ctx.arc(point.x, point.y, selectedFarm === farm.id ? 12 : 8, 0, 2 * Math.PI);
          ctx.fillStyle = color;
          ctx.fill();
          ctx.strokeStyle = '#ffffff';
          ctx.lineWidth = 2;
          ctx.stroke();

          // Desenhar label se selecionado
          if (selectedFarm === farm.id) {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(point.x + 15, point.y - 10, 120, 20);
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.fillText(farm.name, point.x + 20, point.y + 3);
          }
        }
      }
    });

    // Desenhar marcadores das imagens de drone
    if (showDroneOverlays) {
      droneImages.forEach(image => {
        const point = latLngToPixel(image.latitude, image.longitude, width, height);
        if (point.x >= -20 && point.x <= width + 20 && point.y >= -20 && point.y <= height + 20) {
          // Desenhar quadrado roxo
          ctx.fillStyle = '#8b5cf6';
          ctx.fillRect(point.x - 6, point.y - 6, 12, 12);
          ctx.strokeStyle = '#ffffff';
          ctx.lineWidth = 2;
          ctx.strokeRect(point.x - 6, point.y - 6, 12, 12);

          // Ícone de câmera
          ctx.fillStyle = '#ffffff';
          ctx.font = '10px Arial';
          ctx.textAlign = 'center';
          ctx.fillText('📷', point.x, point.y + 3);
          ctx.textAlign = 'left';

          // Label se selecionado
          if (selectedDroneImage === image.id) {
            ctx.fillStyle = 'rgba(139, 92, 246, 0.9)';
            ctx.fillRect(point.x + 15, point.y - 15, 140, 30);
            ctx.fillStyle = '#ffffff';
            ctx.font = '11px Arial';
            ctx.fillText(image.filename, point.x + 20, point.y - 5);
            ctx.fillText(`NDVI: ${image.ndvi_data?.mean.toFixed(2) || 'N/A'}`, point.x + 20, point.y + 8);
          }
        }
      });
    }

    // Desenhar marcador da localização do usuário
    if (userLocation) {
      const point = latLngToPixel(userLocation[0], userLocation[1], width, height);
      if (point.x >= -20 && point.x <= width + 20 && point.y >= -20 && point.y <= height + 20) {
        // Círculo pulsante
        const time = Date.now() / 1000;
        const pulse = Math.sin(time * 3) * 0.3 + 0.7;
        
        ctx.beginPath();
        ctx.arc(point.x, point.y, 8 * pulse, 0, 2 * Math.PI);
        ctx.fillStyle = `rgba(59, 130, 246, ${pulse})`;
        ctx.fill();
        
        ctx.beginPath();
        ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
        ctx.fillStyle = '#3b82f6';
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    }

    // Desenhar informações do mapa
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, height - 60, 200, 50);
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    ctx.fillText(`Centro: ${mapCenter[0].toFixed(4)}, ${mapCenter[1].toFixed(4)}`, 15, height - 40);
    ctx.fillText(`Zoom: ${mapZoom}`, 15, height - 25);
    ctx.fillText(`Camada: ${selectedLayer}`, 15, height - 10);
  }, [mapCenter, mapZoom, selectedLayer, farms, droneImages, showDroneOverlays, selectedFarm, selectedDroneImage, userLocation, latLngToPixel]);

  // Redesenhar quando necessário
  useEffect(() => {
    drawMap();
    const interval = setInterval(drawMap, 100); // Para animação de pulse
    return () => clearInterval(interval);
  }, [drawMap]);

  // Inicializar e redimensionar canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;

    if (canvas && container) {
      // Definir tamanho do canvas
      const rect = container.getBoundingClientRect();
      canvas.width = rect.width || 800;
      canvas.height = rect.height || 400;

      // Adicionar event listener para wheel com passive: false
      const handleWheelEvent = (e: WheelEvent) => {
        e.preventDefault();
        const zoomDelta = e.deltaY > 0 ? -1 : 1;
        const newZoom = Math.max(2, Math.min(18, mapZoom + zoomDelta));
        setMapZoom(newZoom);
      };

      canvas.addEventListener('wheel', handleWheelEvent, { passive: false });

      // Desenhar mapa inicial
      drawMap();

      return () => {
        canvas.removeEventListener('wheel', handleWheelEvent);
      };
    }
  }, [mapZoom, drawMap]);

  // Redimensionar quando container mudar
  useEffect(() => {
    const handleResize = () => {
      const canvas = canvasRef.current;
      const container = containerRef.current;
      if (canvas && container) {
        const rect = container.getBoundingClientRect();
        canvas.width = rect.width || 800;
        canvas.height = rect.height || 400;
        drawMap();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [drawMap]);

  // Função para obter localização GPS
  const getCurrentLocation = useCallback(() => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        const newLocation: [number, number] = [latitude, longitude];

        console.log('Localização obtida:', {
          latitude,
          longitude,
          accuracy: `${accuracy}m`,
          timestamp: new Date(position.timestamp).toLocaleString()
        });

        setUserLocation(newLocation);
        setMapCenter(newLocation);
        setMapZoom(12);

        setLocationError(`Localização obtida com precisão de ${accuracy.toFixed(0)}m`);
        setTimeout(() => setLocationError(null), 3000);
      },
      (error) => {
        console.error('Erro de geolocalização:', error);
        let errorMessage = 'Erro ao obter localização';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada. Permita o acesso à localização.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível. Verifique se o GPS está ativado.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Timeout ao obter localização.';
            break;
        }
        setLocationError(errorMessage);
      },
      {
        enableHighAccuracy: false,
        timeout: 5000,
        maximumAge: 60000
      }
    );
  }, []);

  // Função para calcular distância entre dois pontos
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Raio da Terra em km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Função para sincronizar com imagem de drone mais próxima
  const syncWithNearestDroneImage = () => {
    if (!userLocation || droneImages.length === 0) return;

    let nearestImage = droneImages[0];
    let minDistance = calculateDistance(
      userLocation[0], userLocation[1],
      nearestImage.latitude, nearestImage.longitude
    );

    droneImages.forEach(image => {
      const distance = calculateDistance(
        userLocation[0], userLocation[1],
        image.latitude, image.longitude
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestImage = image;
      }
    });

    setMapCenter([nearestImage.latitude, nearestImage.longitude]);
    setMapZoom(14);
    setSelectedDroneImage(nearestImage.id);
  };

  // Função para rastreamento contínuo de localização
  const toggleLocationTracking = () => {
    if (isTrackingLocation) {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        setWatchId(null);
      }
      setIsTrackingLocation(false);
    } else {
      if (!navigator.geolocation) {
        setLocationError('Geolocalização não é suportada neste navegador');
        return;
      }

      const id = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newLocation: [number, number] = [latitude, longitude];
          setUserLocation(newLocation);
          setLocationError(null);
        },
        (error) => {
          let errorMessage = 'Erro no rastreamento';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Permissão de localização negada';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Localização indisponível';
              break;
            case error.TIMEOUT:
              errorMessage = 'Timeout no rastreamento';
              break;
          }
          setLocationError(errorMessage);
        },
        {
          enableHighAccuracy: false,
          timeout: 3000,
          maximumAge: 5000
        }
      );

      setWatchId(id);
      setIsTrackingLocation(true);
    }
  };

  // Eventos do mouse para interação com o mapa
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && lastMousePos) {
      const deltaX = e.clientX - lastMousePos.x;
      const deltaY = e.clientY - lastMousePos.y;

      // Converter movimento do mouse para movimento do mapa
      const scale = Math.pow(2, mapZoom);
      const latDelta = deltaY * 360 / (256 * scale);
      const lngDelta = -deltaX * 360 / (256 * scale);

      setMapCenter([mapCenter[0] + latDelta, mapCenter[1] + lngDelta]);
      setLastMousePos({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setLastMousePos(null);
  };



  const handleClick = (e: React.MouseEvent) => {
    if (isDragging) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Verificar clique em fazendas
    farms.forEach(farm => {
      const point = latLngToPixel(farm.coordinates.lat, farm.coordinates.lng, canvas.width, canvas.height);
      const distance = Math.sqrt((x - point.x) ** 2 + (y - point.y) ** 2);
      if (distance <= 12) {
        setSelectedFarm(selectedFarm === farm.id ? null : farm.id);
        setSelectedDroneImage(null);
      }
    });

    // Verificar clique em imagens de drone
    droneImages.forEach(image => {
      const point = latLngToPixel(image.latitude, image.longitude, canvas.width, canvas.height);
      const distance = Math.sqrt((x - point.x) ** 2 + (y - point.y) ** 2);
      if (distance <= 8) {
        setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
        setSelectedFarm(null);
      }
    });
  };

  // Obter localização automaticamente ao carregar
  useEffect(() => {
    if (navigator.geolocation) {
      const timer = setTimeout(() => {
        getCurrentLocation();
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setLocationError('Geolocalização não é suportada neste navegador');
    }
  }, [getCurrentLocation]);

  // Limpar watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const layers = [
    { id: 'hybrid', name: 'Híbrido', icon: Layers },
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'streets', name: 'Ruas', icon: MapPin },
    { id: 'terrain', name: 'Terreno', icon: Target },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mapa Interativo Customizado</h1>
          <p className="text-gray-600">Visualizador de mapas nativo com GPS preciso e controles avançados</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-lg font-semibold text-gray-900">Visualização</h3>
                  <div className="flex items-center space-x-2">
                    {layers.map((layer) => {
                      const Icon = layer.icon;
                      return (
                        <button
                          key={layer.id}
                          onClick={() => setSelectedLayer(layer.id)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                            selectedLayer === layer.id
                              ? 'bg-emerald-100 text-emerald-700'
                              : 'text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{layer.name}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                    className={`p-2 rounded-lg transition-colors ${
                      showDroneOverlays
                        ? 'text-purple-600 bg-purple-100 hover:bg-purple-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Mostrar/Ocultar imagens de drone"
                  >
                    <Camera className="w-5 h-5" />
                  </button>
                  <button
                    onClick={syncWithNearestDroneImage}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Sincronizar com imagem de drone mais próxima"
                  >
                    <RefreshCw className="w-5 h-5" />
                  </button>
                  <button
                    onClick={toggleLocationTracking}
                    className={`p-2 rounded-lg transition-colors ${
                      isTrackingLocation
                        ? 'text-green-600 bg-green-100 hover:bg-green-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title={isTrackingLocation ? "Parar rastreamento" : "Iniciar rastreamento GPS"}
                  >
                    <Target className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Filtros"
                  >
                    <Filter className="w-5 h-5" />
                  </button>
                  <button
                    onClick={getCurrentLocation}
                    className={`p-2 rounded-lg transition-colors ${
                      userLocation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Minha localização"
                  >
                    <Navigation className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      setMapCenter([-12.5489, -55.7183]);
                      setMapZoom(6);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Centralizar no Brasil"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Mapa Canvas */}
              <div className="relative h-96" ref={containerRef}>
                {locationError && (
                  <div className={`absolute top-2 left-2 z-10 px-3 py-2 rounded-lg text-sm flex items-center space-x-2 ${
                    locationError.includes('obtida')
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <AlertCircle className="w-4 h-4" />
                    <span>{locationError}</span>
                  </div>
                )}

                {/* Debug Info */}
                {userLocation && (
                  <div className="absolute top-2 right-2 z-10 bg-blue-100 border border-blue-400 text-blue-700 px-3 py-2 rounded-lg text-xs">
                    <div><strong>GPS:</strong> {userLocation[0].toFixed(6)}, {userLocation[1].toFixed(6)}</div>
                    <div><strong>Zoom:</strong> {mapZoom}</div>
                    {isTrackingLocation && <div className="text-green-600"><strong>🎯 Rastreando</strong></div>}
                  </div>
                )}

                <canvas
                  ref={canvasRef}
                  className="w-full h-full cursor-grab active:cursor-grabbing"
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseUp}
                  onClick={handleClick}
                />

                {/* Controles de Zoom */}
                <div className="absolute bottom-4 right-4 z-10 flex flex-col space-y-1">
                  <button
                    onClick={() => setMapZoom(Math.min(18, mapZoom + 1))}
                    className="bg-white border border-gray-300 p-2 rounded-t-lg shadow-sm hover:bg-gray-50"
                    title="Zoom In"
                  >
                    <ZoomIn className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setMapZoom(Math.max(2, mapZoom - 1))}
                    className="bg-white border border-gray-300 p-2 rounded-b-lg shadow-sm hover:bg-gray-50"
                    title="Zoom Out"
                  >
                    <ZoomOut className="w-4 h-4" />
                  </button>
                </div>

                {/* Botão de Localização */}
                <button
                  onClick={getCurrentLocation}
                  className="absolute bottom-4 left-4 z-10 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Obter minha localização"
                >
                  <Crosshair className="w-5 h-5" />
                </button>
              </div>

              {/* Filters Panel */}
              {showFilters && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-sm border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Location Info */}
            <LocationInfo
              position={userLocation}
              onLocationUpdate={(data) => {
                setUserLocation([data.latitude, data.longitude]);
                setMapCenter([data.latitude, data.longitude]);
              }}
            />

            {/* Drone Images List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Imagens de Drone</h3>
                <button
                  onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                  className={`text-xs px-2 py-1 rounded-full ${
                    showDroneOverlays
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {showDroneOverlays ? 'Visível' : 'Oculto'}
                </button>
              </div>
              <div className="space-y-3">
                {droneImages.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedDroneImage === image.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
                      setMapCenter([image.latitude, image.longitude]);
                      setMapZoom(14);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm">📷 {image.filename}</h4>
                      <span className="text-xs text-gray-500">
                        {new Date(image.capture_date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>Altitude: {image.altitude}m</p>
                      {image.ndvi_data && (
                        <p>NDVI: {image.ndvi_data.mean.toFixed(2)}</p>
                      )}
                      {image.analysis_results && (
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${
                            image.analysis_results.vegetation_health === 'healthy' ? 'bg-green-500' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                          <span>{
                            image.analysis_results.vegetation_health === 'healthy' ? 'Saudável' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'Estressada' : 'Crítica'
                          }</span>
                        </div>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {calculateDistance(
                            userLocation[0], userLocation[1],
                            image.latitude, image.longitude
                          ).toFixed(1)} km
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Farm List */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              <div className="space-y-3">
                {farms.map((farm) => (
                  <div
                    key={farm.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedFarm === farm.id
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedFarm(selectedFarm === farm.id ? null : farm.id);
                      setMapCenter([farm.coordinates.lat, farm.coordinates.lng]);
                      setMapZoom(12);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{farm.name}</h4>
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getStatusColor(farm.status) }}
                      />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Área: {farm.area} ha</p>
                      <p>NDVI: {farm.ndvi}</p>
                      <p>Status: {getStatusLabel(farm.status)}</p>
                      {farm.alerts > 0 && (
                        <p className="text-yellow-600">{farm.alerts} alerta(s)</p>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {calculateDistance(
                            userLocation[0], userLocation[1],
                            farm.coordinates.lat, farm.coordinates.lng
                          ).toFixed(1)} km
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Instruções de Uso */}
            <div className="bg-blue-50 rounded-xl border border-blue-200 p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Como Usar</h4>
              <div className="text-xs text-blue-700 space-y-1">
                <p>• <strong>Arrastar:</strong> Mova o mapa clicando e arrastando</p>
                <p>• <strong>Zoom:</strong> Use a roda do mouse ou botões +/-</p>
                <p>• <strong>Clique:</strong> Selecione fazendas ou imagens de drone</p>
                <p>• <strong>GPS:</strong> Botão azul para localização atual</p>
                <p>• <strong>Rastreamento:</strong> Ícone de alvo para GPS contínuo</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapViewCustom;
