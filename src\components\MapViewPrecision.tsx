import React, { useState, useEffect, useRef, useCallback } from 'react';
import { MapPin, Layers, Satellite, Navigation, Filter, Crosshair, AlertCircle, Camera, RefreshCw, Target, RotateCcw, Zap, MapIcon } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er, <PERSON>up, useMap, useMapEvents, Circle, Polyline, ImageOverlay, Rectangle } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import LocationInfo from './LocationInfo';
import { useFarms } from '../hooks/useFarms';

// Configurar ícones do Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Interface para imagens de drone
interface DroneImage {
  id: number;
  filename: string;
  latitude: number;
  longitude: number;
  altitude: number;
  capture_date: string;
  bounds: [[number, number], [number, number]];
  imageUrl: string;
  ndvi_data?: {
    mean: number;
    min: number;
    max: number;
  };
  analysis_results?: {
    vegetation_health: 'healthy' | 'stressed' | 'critical';
    pest_detection: boolean;
    disease_detection: boolean;
  };
}

// Interface para localização de alta precisão
interface PrecisionLocation {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

// Criar ícones customizados de alta qualidade
const createPrecisionIcon = (color: string, size: number = 20) => {
  return L.divIcon({
    className: 'precision-marker',
    html: `
      <div style="
        background: radial-gradient(circle, ${color} 0%, ${color}dd 70%, ${color}aa 100%);
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 3px 8px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.3);
        position: relative;
      ">
        <div style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: ${size * 0.4}px;
          height: ${size * 0.4}px;
          background: white;
          border-radius: 50%;
          opacity: 0.8;
        "></div>
      </div>
    `,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2]
  });
};

const farmIcons = {
  healthy: createPrecisionIcon('#10b981', 24),
  warning: createPrecisionIcon('#f59e0b', 24),
  critical: createPrecisionIcon('#ef4444', 24),
  user: L.divIcon({
    className: 'user-location-marker',
    html: `
      <div style="
        background: radial-gradient(circle, #3b82f6 0%, #1d4ed8 100%);
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 3px 8px rgba(0,0,0,0.4);
        animation: pulse-location 2s infinite;
        position: relative;
      ">
        <div style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          background: white;
          border-radius: 50%;
        "></div>
      </div>
    `,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  }),
  drone: L.divIcon({
    className: 'drone-marker',
    html: `
      <div style="
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        width: 28px;
        height: 28px;
        border-radius: 6px;
        border: 3px solid white;
        box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(45deg);
      ">
        <span style="
          color: white;
          font-size: 14px;
          transform: rotate(-45deg);
          font-weight: bold;
        ">📷</span>
      </div>
    `,
    iconSize: [28, 28],
    iconAnchor: [14, 14]
  })
};

// Componente para atualização precisa do mapa
const PrecisionMapUpdater: React.FC<{ 
  center: [number, number]; 
  zoom: number; 
  userLocation: PrecisionLocation | null;
}> = ({ center, zoom, userLocation }) => {
  const map = useMap();

  useEffect(() => {
    map.setView(center, zoom, { animate: true, duration: 0.5 });
  }, [center, zoom, map]);

  // Atualizar localização do usuário com animação suave
  useEffect(() => {
    if (userLocation) {
      const newCenter: [number, number] = [userLocation.latitude, userLocation.longitude];
      map.setView(newCenter, Math.max(map.getZoom(), 16), { 
        animate: true, 
        duration: 1.0,
        easeLinearity: 0.1
      });
    }
  }, [userLocation, map]);

  return null;
};

// Componente para localização do usuário com precisão
const PrecisionLocationMarker: React.FC<{ 
  location: PrecisionLocation | null;
  showAccuracyCircle: boolean;
}> = ({ location, showAccuracyCircle }) => {
  if (!location) return null;

  const position: [number, number] = [location.latitude, location.longitude];

  return (
    <>
      {/* Círculo de precisão */}
      {showAccuracyCircle && (
        <Circle
          center={position}
          radius={location.accuracy}
          pathOptions={{
            color: '#3b82f6',
            fillColor: '#3b82f6',
            fillOpacity: 0.1,
            weight: 2,
            opacity: 0.6
          }}
        />
      )}
      
      {/* Marcador do usuário */}
      <Marker position={position} icon={farmIcons.user}>
        <Popup>
          <div className="text-center p-2">
            <strong>📍 Sua Localização Precisa</strong>
            <div className="text-xs mt-2 space-y-1">
              <div><strong>Coordenadas:</strong> {location.latitude.toFixed(8)}, {location.longitude.toFixed(8)}</div>
              <div><strong>Precisão:</strong> ±{location.accuracy.toFixed(1)}m</div>
              {location.altitude && (
                <div><strong>Altitude:</strong> {location.altitude.toFixed(1)}m</div>
              )}
              {location.speed && (
                <div><strong>Velocidade:</strong> {(location.speed * 3.6).toFixed(1)} km/h</div>
              )}
              {location.heading && (
                <div><strong>Direção:</strong> {location.heading.toFixed(0)}°</div>
              )}
              <div><strong>Timestamp:</strong> {new Date(location.timestamp).toLocaleTimeString('pt-BR')}</div>
            </div>
          </div>
        </Popup>
      </Marker>
    </>
  );
};

const MapViewPrecision: React.FC = () => {
  const { farms, loading: farmsLoading } = useFarms();
  const [selectedLayer, setSelectedLayer] = useState('hybrid');
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [userLocation, setUserLocation] = useState<PrecisionLocation | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([-12.5489, -55.7183]);
  const [mapZoom, setMapZoom] = useState(10);
  const [droneImages, setDroneImages] = useState<DroneImage[]>([]);
  const [selectedDroneImage, setSelectedDroneImage] = useState<number | null>(null);
  const [showDroneOverlays, setShowDroneOverlays] = useState(true);
  const [isTrackingLocation, setIsTrackingLocation] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);
  const [showAccuracyCircle, setShowAccuracyCircle] = useState(true);
  const [locationHistory, setLocationHistory] = useState<PrecisionLocation[]>([]);
  const [highPrecisionMode, setHighPrecisionMode] = useState(true);
  const mapRef = useRef<L.Map | null>(null);

  // Gerar URLs de imagens simuladas para o mapa
  const generateMapImageUrl = (type: string, id: number) => {
    const colors = {
      rgb: '#4ade80',
      nir: '#8b5cf6',
      multispectral: '#06b6d4',
      thermal: '#ef4444'
    };
    const color = colors[type as keyof typeof colors] || '#6b7280';

    return `data:image/svg+xml;base64,${btoa(`
      <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="grid${id}" width="50" height="50" patternUnits="userSpaceOnUse">
            <path d="M 50 0 L 0 0 0 50" fill="none" stroke="${color}" stroke-width="1" opacity="0.3"/>
          </pattern>
          <radialGradient id="grad${id}" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:${color};stop-opacity:0.3" />
          </radialGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad${id})"/>
        <rect width="100%" height="100%" fill="url(#grid${id})"/>
        <text x="50%" y="40%" text-anchor="middle" font-family="Arial" font-size="32" fill="white" font-weight="bold">
          ${type.toUpperCase()}
        </text>
        <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="18" fill="white">
          Drone ${id}
        </text>
        <text x="50%" y="60%" text-anchor="middle" font-family="Arial" font-size="16" fill="white" opacity="0.8">
          NDVI Analysis
        </text>
        <circle cx="100" cy="100" r="30" fill="white" opacity="0.2"/>
        <circle cx="700" cy="100" r="25" fill="white" opacity="0.2"/>
        <circle cx="400" cy="500" r="40" fill="white" opacity="0.15"/>
      </svg>
    `)}`;
  };

  // TODO: Replace with actual drone images data from API
  // Load drone images from API
  useEffect(() => {
    // TODO: Implement API call to fetch drone images
    setDroneImages([]);
  }, []);

  // Função para obter localização GPS de alta precisão
  const getCurrentLocationPrecision = useCallback(() => {
    setLocationError(null);

    if (!navigator.geolocation) {
      setLocationError('Geolocalização não é suportada neste navegador');
      return;
    }

    const options: PositionOptions = {
      enableHighAccuracy: highPrecisionMode,
      timeout: highPrecisionMode ? 30000 : 10000, // Mais tempo para alta precisão
      maximumAge: highPrecisionMode ? 5000 : 30000 // Cache menor para alta precisão
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy, altitude, altitudeAccuracy, heading, speed } = position.coords;

        const precisionLocation: PrecisionLocation = {
          latitude,
          longitude,
          accuracy,
          altitude: altitude || undefined,
          altitudeAccuracy: altitudeAccuracy || undefined,
          heading: heading || undefined,
          speed: speed || undefined,
          timestamp: position.timestamp
        };

        console.log('Localização de alta precisão obtida:', {
          ...precisionLocation,
          accuracy: `±${accuracy.toFixed(1)}m`,
          timestamp: new Date(position.timestamp).toLocaleString('pt-BR')
        });

        setUserLocation(precisionLocation);
        setMapCenter([latitude, longitude]);
        setMapZoom(18); // Zoom alto para precisão

        // Adicionar ao histórico
        setLocationHistory(prev => [...prev.slice(-9), precisionLocation]); // Manter últimas 10 posições

        const accuracyLevel = accuracy < 5 ? 'excelente' :
                             accuracy < 10 ? 'boa' :
                             accuracy < 20 ? 'moderada' : 'baixa';

        setLocationError(`Localização obtida com precisão ${accuracyLevel} (±${accuracy.toFixed(1)}m)`);
        setTimeout(() => setLocationError(null), 5000);
      },
      (error) => {
        console.error('Erro de geolocalização de alta precisão:', error);
        let errorMessage = 'Erro ao obter localização precisa';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Permissão de localização negada. Ative a localização nas configurações.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Localização indisponível. Verifique se o GPS está ativo e você está ao ar livre.';
            break;
          case error.TIMEOUT:
            errorMessage = `Timeout na localização ${highPrecisionMode ? 'de alta precisão' : ''}. Tentando modo padrão...`;
            if (highPrecisionMode) {
              // Fallback para modo padrão
              setHighPrecisionMode(false);
              setTimeout(() => getCurrentLocationPrecision(), 1000);
              return;
            }
            break;
        }
        setLocationError(errorMessage);
      },
      options
    );
  }, [highPrecisionMode]);

  // Função para calcular distância precisa entre dois pontos (Haversine)
  const calculatePreciseDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371000; // Raio da Terra em metros (mais preciso)
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distância em metros
  };

  // Função para sincronizar com imagem de drone mais próxima
  const syncWithNearestDroneImage = () => {
    if (!userLocation || droneImages.length === 0) return;

    let nearestImage = droneImages[0];
    let minDistance = calculatePreciseDistance(
      userLocation.latitude, userLocation.longitude,
      nearestImage.latitude, nearestImage.longitude
    );

    droneImages.forEach(image => {
      const distance = calculatePreciseDistance(
        userLocation.latitude, userLocation.longitude,
        image.latitude, image.longitude
      );
      if (distance < minDistance) {
        minDistance = distance;
        nearestImage = image;
      }
    });

    setMapCenter([nearestImage.latitude, nearestImage.longitude]);
    setMapZoom(20); // Zoom máximo para análise detalhada
    setSelectedDroneImage(nearestImage.id);
  };

  // Função para rastreamento contínuo de alta precisão
  const togglePrecisionTracking = () => {
    if (isTrackingLocation) {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        setWatchId(null);
      }
      setIsTrackingLocation(false);
    } else {
      if (!navigator.geolocation) {
        setLocationError('Geolocalização não é suportada neste navegador');
        return;
      }

      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 2000 // Atualização frequente para rastreamento
      };

      const id = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude, accuracy, altitude, altitudeAccuracy, heading, speed } = position.coords;

          const precisionLocation: PrecisionLocation = {
            latitude,
            longitude,
            accuracy,
            altitude: altitude || undefined,
            altitudeAccuracy: altitudeAccuracy || undefined,
            heading: heading || undefined,
            speed: speed || undefined,
            timestamp: position.timestamp
          };

          setUserLocation(precisionLocation);
          setLocationHistory(prev => [...prev.slice(-19), precisionLocation]); // Manter últimas 20 posições
          setLocationError(null);
        },
        (error) => {
          let errorMessage = 'Erro no rastreamento preciso';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Permissão de localização negada';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Localização indisponível para rastreamento';
              break;
            case error.TIMEOUT:
              errorMessage = 'Timeout no rastreamento (continuando...)';
              return; // Não parar o rastreamento por timeout
          }
          setLocationError(errorMessage);
        },
        options
      );

      setWatchId(id);
      setIsTrackingLocation(true);
    }
  };

  // Obter localização automaticamente ao carregar
  useEffect(() => {
    if (navigator.geolocation) {
      const timer = setTimeout(() => {
        getCurrentLocationPrecision();
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setLocationError('Geolocalização não é suportada neste navegador');
    }
  }, [getCurrentLocationPrecision]);

  // Limpar watch quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  // Função para alternar sobreposição de imagem no mapa
  const toggleImageOverlay = (imageId: number) => {
    setDroneImages(prev => prev.map(img =>
      img.id === imageId
        ? { ...img, show_on_map: !img.show_on_map }
        : img
    ));
  };

  // Função para ajustar opacidade da sobreposição
  const handleOverlayOpacityChange = (imageId: number, opacity: number) => {
    setDroneImages(prev => prev.map(img =>
      img.id === imageId
        ? { ...img, overlay_opacity: opacity }
        : img
    ));
  };

  const layers = [
    { id: 'hybrid', name: 'Híbrido', icon: Layers },
    { id: 'satellite', name: 'Satélite', icon: Satellite },
    { id: 'streets', name: 'Ruas', icon: MapPin },
    { id: 'terrain', name: 'Terreno', icon: MapIcon },
  ];

  const getTileLayerUrl = () => {
    switch (selectedLayer) {
      case 'satellite':
        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
      case 'terrain':
        return 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png';
      case 'streets':
        return 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
      case 'hybrid':
      default:
        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
    }
  };

  const getTileLayerAttribution = () => {
    switch (selectedLayer) {
      case 'satellite':
      case 'hybrid':
        return '&copy; <a href="https://www.esri.com/">Esri</a>';
      case 'terrain':
        return '&copy; <a href="https://opentopomap.org/">OpenTopoMap</a>';
      case 'streets':
      default:
        return '&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#10b981';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saudável';
      case 'warning': return 'Atenção';
      case 'critical': return 'Crítico';
      default: return 'Desconhecido';
    }
  };

  return (
    <div className="p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-4 sm:mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 sm:mb-2">Mapa de Precisão Agrícola</h1>
          <p className="text-sm sm:text-base text-gray-600">OpenStreetMap + Leaflet com GPS de alta precisão e análise geoespacial avançada</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
          {/* Map Container */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Map Controls */}
              <div className="p-3 sm:p-4 border-b border-gray-200">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">Visualização</h3>
                    <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                      {layers.map((layer) => {
                        const Icon = layer.icon;
                        return (
                          <button
                            key={layer.id}
                            onClick={() => setSelectedLayer(layer.id)}
                            className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
                              selectedLayer === layer.id
                                ? 'bg-emerald-100 text-emerald-700'
                                : 'text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            <Icon className="w-3 sm:w-4 h-3 sm:h-4" />
                            <span className="hidden sm:inline">{layer.name}</span>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center gap-1 justify-center lg:justify-end">
                    <button
                      onClick={() => setHighPrecisionMode(!highPrecisionMode)}
                      className={`p-1.5 sm:p-2 rounded-lg transition-colors ${
                        highPrecisionMode
                          ? 'text-yellow-600 bg-yellow-100 hover:bg-yellow-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      title={highPrecisionMode ? "Modo Alta Precisão ATIVO" : "Ativar Modo Alta Precisão"}
                    >
                      <Zap className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={() => setShowAccuracyCircle(!showAccuracyCircle)}
                      className={`p-1.5 sm:p-2 rounded-lg transition-colors ${
                        showAccuracyCircle
                          ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      title="Mostrar/Ocultar círculo de precisão"
                    >
                      <Crosshair className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                      className={`p-1.5 sm:p-2 rounded-lg transition-colors ${
                        showDroneOverlays
                          ? 'text-purple-600 bg-purple-100 hover:bg-purple-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      title="Mostrar/Ocultar imagens de drone"
                    >
                      <Camera className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={syncWithNearestDroneImage}
                      className="p-1.5 sm:p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Sincronizar com imagem de drone mais próxima"
                    >
                      <RefreshCw className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={togglePrecisionTracking}
                      className={`p-1.5 sm:p-2 rounded-lg transition-colors ${
                        isTrackingLocation
                          ? 'text-green-600 bg-green-100 hover:bg-green-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      title={isTrackingLocation ? "Parar rastreamento preciso" : "Iniciar rastreamento preciso"}
                    >
                      <Target className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className="p-1.5 sm:p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Filtros"
                    >
                      <Filter className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={getCurrentLocationPrecision}
                      className={`p-1.5 sm:p-2 rounded-lg transition-colors ${
                        userLocation
                          ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      title="Obter localização precisa"
                    >
                      <Navigation className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                    <button
                      onClick={() => {
                        setMapCenter([-12.5489, -55.7183]);
                        setMapZoom(10);
                      }}
                      className="p-1.5 sm:p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Centralizar no Brasil"
                    >
                      <RotateCcw className="w-4 sm:w-5 h-4 sm:h-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Mapa Leaflet de Alta Precisão */}
              <div className="relative h-64 sm:h-80 lg:h-96">
                {locationError && (
                  <div className={`absolute top-2 left-2 z-[1000] px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm flex items-center space-x-1 sm:space-x-2 max-w-xs sm:max-w-sm ${
                    locationError.includes('obtida') || locationError.includes('excelente') || locationError.includes('boa')
                      ? 'bg-green-100 border border-green-400 text-green-700'
                      : locationError.includes('moderada')
                      ? 'bg-yellow-100 border border-yellow-400 text-yellow-700'
                      : 'bg-red-100 border border-red-400 text-red-700'
                  }`}>
                    <AlertCircle className="w-3 sm:w-4 h-3 sm:h-4 flex-shrink-0" />
                    <span className="truncate">{locationError}</span>
                  </div>
                )}

                {/* Informações de Precisão */}
                {userLocation && (
                  <div className="absolute top-2 right-2 z-[1000] bg-blue-100 border border-blue-400 text-blue-700 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs max-w-xs">
                    <div className="space-y-1">
                      <div><strong>GPS:</strong> {userLocation.latitude.toFixed(6)}, {userLocation.longitude.toFixed(6)}</div>
                      <div><strong>Precisão:</strong> ±{userLocation.accuracy.toFixed(1)}m</div>
                      {userLocation.altitude && <div><strong>Alt:</strong> {userLocation.altitude.toFixed(1)}m</div>}
                      {userLocation.speed && <div><strong>Vel:</strong> {(userLocation.speed * 3.6).toFixed(1)} km/h</div>}
                      <div><strong>Zoom:</strong> {mapZoom}</div>
                      {isTrackingLocation && <div className="text-green-600"><strong>🎯 Ativo</strong></div>}
                      {highPrecisionMode && <div className="text-yellow-600"><strong>⚡ Alta Precisão</strong></div>}
                    </div>
                  </div>
                )}

                <MapContainer
                  center={mapCenter}
                  zoom={mapZoom}
                  style={{ height: '100%', width: '100%' }}
                  ref={mapRef}
                  zoomControl={false}
                  attributionControl={true}
                >
                  {/* Componente para atualização precisa do mapa */}
                  <PrecisionMapUpdater center={mapCenter} zoom={mapZoom} userLocation={userLocation} />

                  {/* Camada base */}
                  <TileLayer
                    url={getTileLayerUrl()}
                    attribution={getTileLayerAttribution()}
                    maxZoom={20}
                    tileSize={256}
                  />

                  {/* Camada híbrida adicional */}
                  {selectedLayer === 'hybrid' && (
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://openstreetmap.org/">OpenStreetMap</a>'
                      opacity={0.3}
                      maxZoom={20}
                    />
                  )}

                  {/* Trilha de localização (histórico) */}
                  {locationHistory.length > 1 && (
                    <Polyline
                      positions={locationHistory.map(loc => [loc.latitude, loc.longitude] as [number, number])}
                      pathOptions={{
                        color: '#3b82f6',
                        weight: 3,
                        opacity: 0.7,
                        dashArray: '5, 5'
                      }}
                    />
                  )}

                  {/* Marcadores das Fazendas */}
                  {farms.map((farm) => (
                    <Marker
                      key={farm.id}
                      position={[farm.coordinates.lat, farm.coordinates.lng]}
                      icon={farmIcons[farm.status as keyof typeof farmIcons]}
                      eventHandlers={{
                        click: () => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)
                      }}
                    >
                      <Popup>
                        <div className="p-3 min-w-[280px]">
                          <h3 className="font-semibold text-gray-900 mb-2">{farm.name}</h3>
                          <p className="text-sm text-gray-600 mb-3">{farm.description}</p>
                          <div className="space-y-2 text-xs">
                            <div><strong>Proprietário:</strong> {farm.owner}</div>
                            <div><strong>Área:</strong> {farm.area} ha</div>
                            <div><strong>NDVI:</strong> {farm.ndvi}</div>
                            <div><strong>Status:</strong> {getStatusLabel(farm.status)}</div>
                            {farm.alerts > 0 && (
                              <div className="text-yellow-600"><strong>Alertas:</strong> {farm.alerts}</div>
                            )}
                            {userLocation && (
                              <div className="bg-blue-50 p-2 rounded mt-2">
                                <strong>Distância Precisa:</strong> {
                                  (calculatePreciseDistance(
                                    userLocation.latitude, userLocation.longitude,
                                    farm.coordinates.lat, farm.coordinates.lng
                                  ) / 1000).toFixed(3)
                                } km
                              </div>
                            )}
                          </div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}

                  {/* Sobreposições de Imagens de Drone */}
                  {showDroneOverlays && droneImages.map((image) => (
                    <React.Fragment key={`drone-overlay-${image.id}`}>
                      {/* Sobreposição da imagem se habilitada */}
                      {image.show_on_map && image.bounds && image.imageUrl && (
                        <ImageOverlay
                          url={image.imageUrl}
                          bounds={image.bounds}
                          opacity={image.overlay_opacity || 0.7}
                        />
                      )}

                      {/* Retângulo de contorno da área */}
                      {image.bounds && (
                        <Rectangle
                          bounds={image.bounds}
                          pathOptions={{
                            color: image.show_on_map ? '#8b5cf6' : '#6b7280',
                            weight: image.show_on_map ? 3 : 2,
                            opacity: image.show_on_map ? 0.8 : 0.5,
                            fillOpacity: 0,
                            dashArray: image.show_on_map ? undefined : '5, 5'
                          }}
                        />
                      )}

                      {/* Marcador central da imagem */}
                      <Marker
                        position={[image.latitude, image.longitude]}
                        icon={farmIcons.drone}
                        eventHandlers={{
                          click: () => setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id)
                        }}
                      >
                        <Popup maxWidth={400} maxHeight={500} className="drone-popup">
                          <div className="p-2 max-w-sm">
                            <h3 className="font-semibold text-gray-900 mb-2 text-sm">📷 {image.filename}</h3>

                            {/* Preview da imagem */}
                            {image.imageUrl && (
                              <div className="mb-3">
                                <img
                                  src={image.imageUrl}
                                  alt={image.filename}
                                  className="w-full h-24 object-cover rounded border"
                                />
                              </div>
                            )}

                            <div className="space-y-2 text-xs">
                              <div><strong>Data:</strong> {new Date(image.capture_date).toLocaleDateString('pt-BR')}</div>
                              <div><strong>Altitude:</strong> {image.altitude}m</div>
                              <div><strong>Coordenadas:</strong> {image.latitude.toFixed(6)}, {image.longitude.toFixed(6)}</div>

                              {/* Status da sobreposição */}
                              <div className="bg-gray-50 p-2 rounded">
                                <div><strong>Sobreposição:</strong> {image.show_on_map ? '✅ Ativa' : '❌ Inativa'}</div>
                                {image.show_on_map && (
                                  <div><strong>Opacidade:</strong> {Math.round((image.overlay_opacity || 0.7) * 100)}%</div>
                                )}
                              </div>

                              {image.ndvi_data && (
                                <div className="bg-green-50 p-2 rounded">
                                  <div><strong>NDVI:</strong> {image.ndvi_data.mean.toFixed(3)}</div>
                                  <div><strong>Faixa:</strong> {image.ndvi_data.min.toFixed(3)} - {image.ndvi_data.max.toFixed(3)}</div>
                                </div>
                              )}

                              {image.analysis_results && (
                                <div className="bg-blue-50 p-2 rounded">
                                  <div><strong>Saúde:</strong> {
                                    image.analysis_results.vegetation_health === 'healthy' ? '🟢 Saudável' :
                                    image.analysis_results.vegetation_health === 'stressed' ? '🟡 Estressada' : '🔴 Crítica'
                                  }</div>
                                  <div><strong>Pragas:</strong> {image.analysis_results.pest_detection ? '⚠️ Sim' : '✅ Não'}</div>
                                  <div><strong>Doenças:</strong> {image.analysis_results.disease_detection ? '⚠️ Sim' : '✅ Não'}</div>
                                </div>
                              )}

                              {userLocation && (
                                <div className="bg-purple-50 p-2 rounded">
                                  <strong>Distância:</strong> {
                                    calculatePreciseDistance(
                                      userLocation.latitude, userLocation.longitude,
                                      image.latitude, image.longitude
                                    ).toFixed(1)
                                  }m da sua localização
                                </div>
                              )}
                            </div>
                          </div>
                        </Popup>
                      </Marker>
                    </React.Fragment>
                  ))}

                  {/* Marcadores das Fazendas */}
                  {farms.map((farm) => (
                    farm.latitude && farm.longitude && (
                      <Marker
                        key={farm.id}
                        position={[farm.latitude, farm.longitude]}
                        icon={farmIcons.healthy}
                        eventHandlers={{
                          click: () => setSelectedFarm(selectedFarm === farm.id ? null : farm.id)
                        }}
                      >
                        <Popup>
                          <div className="p-2">
                            <h3 className="font-semibold text-gray-900">{farm.name}</h3>
                            <div className="space-y-1 text-xs mt-2">
                              <div><strong>Área:</strong> {farm.total_area} ha</div>
                              <div><strong>Tipo:</strong> {farm.crop_type}</div>
                              <div><strong>Localização:</strong> {farm.address}</div>
                              <div><strong>Proprietário:</strong> {farm.owner_name}</div>
                              {userLocation && (
                                <div><strong>Distância:</strong> {
                                  (calculatePreciseDistance(
                                    userLocation.latitude, userLocation.longitude,
                                    farm.latitude, farm.longitude
                                  ) / 1000).toFixed(3)
                                } km</div>
                              )}
                            </div>
                          </div>
                        </Popup>
                      </Marker>
                    )
                  ))}

                  {/* Marcador da Localização do Usuário com Precisão */}
                  <PrecisionLocationMarker location={userLocation} showAccuracyCircle={showAccuracyCircle} />
                </MapContainer>

                {/* Controles de Zoom Customizados */}
                <div className="absolute bottom-3 sm:bottom-4 right-3 sm:right-4 z-[1000] flex flex-col space-y-1">
                  <button
                    onClick={() => setMapZoom(Math.min(20, mapZoom + 1))}
                    className="bg-white border border-gray-300 p-1.5 sm:p-2 rounded-t-lg shadow-sm hover:bg-gray-50 text-gray-700 text-sm sm:text-base"
                    title="Zoom In"
                  >
                    +
                  </button>
                  <button
                    onClick={() => setMapZoom(Math.max(2, mapZoom - 1))}
                    className="bg-white border border-gray-300 p-1.5 sm:p-2 rounded-b-lg shadow-sm hover:bg-gray-50 text-gray-700 text-sm sm:text-base"
                    title="Zoom Out"
                  >
                    -
                  </button>
                </div>

                {/* Botão de Localização Precisa */}
                <button
                  onClick={getCurrentLocationPrecision}
                  className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 z-[1000] bg-blue-600 text-white p-2 sm:p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                  title="Obter localização de alta precisão"
                >
                  <Crosshair className="w-4 sm:w-5 h-4 sm:h-5" />
                </button>

                {/* Legenda Avançada */}
                <div className="absolute bottom-14 sm:bottom-16 left-3 sm:left-4 z-[1000] bg-white rounded-lg shadow-lg p-2 sm:p-3 max-w-xs">
                  <h4 className="text-xs sm:text-sm font-medium text-gray-900 mb-2">Legenda</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 sm:w-3 h-2 sm:h-3 rounded-full bg-green-500 flex-shrink-0" />
                      <span className="text-gray-600 truncate">Fazenda Saudável</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 sm:w-3 h-2 sm:h-3 rounded-full bg-yellow-500 flex-shrink-0" />
                      <span className="text-gray-600 truncate">Fazenda Atenção</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 sm:w-3 h-2 sm:h-3 rounded-full bg-red-500 flex-shrink-0" />
                      <span className="text-gray-600 truncate">Fazenda Crítica</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 sm:w-3 h-2 sm:h-3 rounded-full bg-blue-500 flex-shrink-0" />
                      <span className="text-gray-600 truncate">Sua localização</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 sm:w-3 h-2 sm:h-3 rounded bg-purple-500 flex-shrink-0" />
                      <span className="text-gray-600 truncate">Imagem de drone</span>
                    </div>
                    {userLocation && userLocation.accuracy < 10 && (
                      <div className="flex items-center space-x-2 pt-1 border-t border-gray-200">
                        <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse flex-shrink-0" />
                        <span className="text-green-600 font-medium text-xs">GPS Preciso</span>
                      </div>
                    )}
                    {isTrackingLocation && (
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse flex-shrink-0" />
                        <span className="text-blue-600 font-medium text-xs">Rastreamento Ativo</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Painel de Filtros Avançados */}
              {showFilters && (
                <div className="p-3 sm:p-4 border-t border-gray-200 bg-gray-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Filtros de Precisão</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Status da Vegetação</label>
                      <select className="w-full text-xs sm:text-sm border border-gray-300 rounded-lg px-2 sm:px-3 py-1 sm:py-2">
                        <option value="">Todos</option>
                        <option value="healthy">Saudável</option>
                        <option value="warning">Atenção</option>
                        <option value="critical">Crítico</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Faixa NDVI</label>
                      <select className="w-full text-xs sm:text-sm border border-gray-300 rounded-lg px-2 sm:px-3 py-1 sm:py-2">
                        <option value="">Todas</option>
                        <option value="high">Alto (0.7-1.0)</option>
                        <option value="medium">Médio (0.4-0.7)</option>
                        <option value="low">Baixo (0.0-0.4)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Precisão GPS</label>
                      <select className="w-full text-xs sm:text-sm border border-gray-300 rounded-lg px-2 sm:px-3 py-1 sm:py-2">
                        <option value="">Todas</option>
                        <option value="high">Alta (&lt;5m)</option>
                        <option value="medium">Média (5-20m)</option>
                        <option value="low">Baixa (&gt;20m)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Tipo de Cultivo</label>
                      <select className="w-full text-xs sm:text-sm border border-gray-300 rounded-lg px-2 sm:px-3 py-1 sm:py-2">
                        <option value="">Todos</option>
                        <option value="soja">Soja</option>
                        <option value="milho">Milho</option>
                        <option value="algodao">Algodão</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar Avançada */}
          <div className="space-y-6">
            {/* Informações de Localização Precisa */}
            <LocationInfo
              position={userLocation ? [userLocation.latitude, userLocation.longitude] : null}
              onLocationUpdate={(data) => {
                const precisionLocation: PrecisionLocation = {
                  latitude: data.latitude,
                  longitude: data.longitude,
                  accuracy: 5, // Assumir boa precisão para entrada manual
                  timestamp: Date.now()
                };
                setUserLocation(precisionLocation);
                setMapCenter([data.latitude, data.longitude]);
              }}
            />

            {/* Status de Precisão GPS */}
            {userLocation && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status GPS</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Precisão:</span>
                    <span className={`text-sm font-medium ${
                      userLocation.accuracy < 5 ? 'text-green-600' :
                      userLocation.accuracy < 10 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      ±{userLocation.accuracy.toFixed(1)}m
                    </span>
                  </div>
                  {userLocation.altitude && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Altitude:</span>
                      <span className="text-sm font-medium text-gray-900">{userLocation.altitude.toFixed(1)}m</span>
                    </div>
                  )}
                  {userLocation.speed && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Velocidade:</span>
                      <span className="text-sm font-medium text-gray-900">{(userLocation.speed * 3.6).toFixed(1)} km/h</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Modo:</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      highPrecisionMode ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {highPrecisionMode ? 'Alta Precisão' : 'Padrão'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Histórico:</span>
                    <span className="text-sm font-medium text-gray-900">{locationHistory.length} pontos</span>
                  </div>
                </div>
              </div>
            )}

            {/* Lista de Imagens de Drone */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Imagens de Drone</h3>
                <button
                  onClick={() => setShowDroneOverlays(!showDroneOverlays)}
                  className={`text-xs px-2 py-1 rounded-full ${
                    showDroneOverlays
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {showDroneOverlays ? 'Visível' : 'Oculto'}
                </button>
              </div>
              <div className="space-y-3">
                {droneImages.map((image) => (
                  <div
                    key={image.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedDroneImage === image.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => {
                      setSelectedDroneImage(selectedDroneImage === image.id ? null : image.id);
                      setMapCenter([image.latitude, image.longitude]);
                      setMapZoom(20);
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm">📷 {image.filename}</h4>
                      <span className="text-xs text-gray-500">
                        {new Date(image.capture_date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>Altitude: {image.altitude}m</p>
                      {image.ndvi_data && (
                        <p>NDVI: {image.ndvi_data.mean.toFixed(3)}</p>
                      )}
                      {image.analysis_results && (
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${
                            image.analysis_results.vegetation_health === 'healthy' ? 'bg-green-500' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'bg-yellow-500' : 'bg-red-500'
                          }`} />
                          <span>{
                            image.analysis_results.vegetation_health === 'healthy' ? 'Saudável' :
                            image.analysis_results.vegetation_health === 'stressed' ? 'Estressada' : 'Crítica'
                          }</span>
                        </div>
                      )}
                      {userLocation && (
                        <p className="text-blue-600">
                          {(calculatePreciseDistance(
                            userLocation.latitude, userLocation.longitude,
                            image.latitude, image.longitude
                          ) / 1000).toFixed(3)} km
                        </p>
                      )}

                      {/* Controles de Sobreposição */}
                      <div className="pt-2 border-t border-gray-200 space-y-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleImageOverlay(image.id);
                          }}
                          className={`w-full text-xs py-1 px-2 rounded transition-colors ${
                            image.show_on_map
                              ? 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          {image.show_on_map ? '🗺️ Remover do Mapa' : '🗺️ Mostrar no Mapa'}
                        </button>

                        {/* Controle de Opacidade */}
                        {image.show_on_map && (
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">Opacidade:</span>
                            <input
                              type="range"
                              min="0.1"
                              max="1"
                              step="0.1"
                              value={image.overlay_opacity || 0.7}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleOverlayOpacityChange(image.id, parseFloat(e.target.value));
                              }}
                              className="flex-1 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                            />
                            <span className="text-xs text-gray-500 w-8">
                              {Math.round((image.overlay_opacity || 0.7) * 100)}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Lista de Propriedades */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Propriedades</h3>
              {farmsLoading ? (
                <div className="text-center py-4">
                  <div className="text-gray-500">Carregando fazendas...</div>
                </div>
              ) : farms.length === 0 ? (
                <div className="text-center py-4">
                  <div className="text-gray-500">Nenhuma fazenda encontrada.</div>
                  <p className="text-sm text-gray-400 mt-1">Cadastre uma fazenda primeiro.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {farms.map((farm) => (
                    <div
                      key={farm.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedFarm === farm.id
                          ? 'border-emerald-500 bg-emerald-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => {
                        setSelectedFarm(selectedFarm === farm.id ? null : farm.id);
                        if (farm.latitude && farm.longitude) {
                          setMapCenter([farm.latitude, farm.longitude]);
                          setMapZoom(16);
                        }
                      }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{farm.name}</h4>
                        <div className="w-3 h-3 rounded-full bg-green-500" />
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>Área: {farm.total_area} ha</p>
                        <p>Tipo: {farm.crop_type}</p>
                        <p>Localização: {farm.address}</p>
                        {userLocation && farm.latitude && farm.longitude && (
                          <p className="text-blue-600">
                            {(calculatePreciseDistance(
                              userLocation.latitude, userLocation.longitude,
                              farm.latitude, farm.longitude
                            ) / 1000).toFixed(3)} km
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Guia de Precisão */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">🎯 Guia de Precisão GPS</h4>
              <div className="text-xs text-blue-700 space-y-1">
                <p>• <strong>&lt;5m:</strong> Excelente - Ideal para agricultura de precisão</p>
                <p>• <strong>5-10m:</strong> Boa - Adequada para mapeamento geral</p>
                <p>• <strong>10-20m:</strong> Moderada - Útil para navegação básica</p>
                <p>• <strong>&gt;20m:</strong> Baixa - Recomenda-se melhorar sinal</p>
                <p className="pt-1 border-t border-blue-200">
                  <strong>Dica:</strong> Para melhor precisão, use ao ar livre com céu limpo
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapViewPrecision;
