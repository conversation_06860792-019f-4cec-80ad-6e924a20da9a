import React from 'react';
import { Home, Map, BarChart3, FileText, Leaf, <PERSON>tings, User, MapPin, Camera } from 'lucide-react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, onViewChange }) => {
  const menuItems = [
    { id: 'dashboard', icon: Home, label: 'Dashboard', color: 'text-emerald-600' },
    { id: 'mapa', icon: Map, label: 'Mapa Interativo', color: 'text-blue-600' },
    { id: 'fazendas', icon: MapPin, label: 'Gestão de Fazendas', color: 'text-green-600' },
    { id: 'analise-imagens', icon: Camera, label: 'Análise de Imagens', color: 'text-indigo-600' },
    { id: 'analytics', icon: BarChart3, label: 'Análise Avançada', color: 'text-purple-600' },
    { id: 'relatorios', icon: FileText, label: '<PERSON>lat<PERSON><PERSON><PERSON>', color: 'text-orange-600' },
  ];



  return (
    <div className="w-80 lg:w-80 md:w-72 bg-white shadow-xl border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 sm:p-6 border-b border-gray-100">
        <div className="flex items-center space-x-2 sm:space-x-3 mb-3 sm:mb-4">
          <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
            <Leaf className="w-4 sm:w-6 h-4 sm:h-6 text-white" />
          </div>
          <div className="min-w-0 flex-1">
            <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">AgroPrecision</h1>
            <p className="text-xs sm:text-sm text-gray-500 truncate">Monitoramento Inteligente</p>
          </div>
        </div>

        <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-3 sm:p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">Fazenda São João</span>
            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-medium flex-shrink-0">AA+</span>
          </div>
          <p className="text-xs text-gray-600 mb-2 sm:mb-3">Propriedade agrícola de alta tecnologia</p>
          <div className="text-xl sm:text-2xl font-bold text-gray-900">4.250 ha</div>
          <p className="text-xs text-gray-500">Área total cadastrada</p>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-3 sm:p-4 overflow-y-auto">
        <div className="space-y-1 sm:space-y-2 mb-4 sm:mb-6">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={`w-full flex items-center space-x-2 sm:space-x-3 px-3 sm:px-4 py-2 sm:py-3 rounded-xl text-left transition-all duration-200 ${
                  isActive
                    ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-lg'
                    : 'text-gray-700 hover:bg-gray-50 hover:shadow-sm'
                }`}
              >
                <Icon className={`w-4 sm:w-5 h-4 sm:h-5 flex-shrink-0 ${isActive ? 'text-white' : item.color}`} />
                <span className="font-medium text-sm sm:text-base truncate">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>

      {/* User Profile */}
      <div className="p-3 sm:p-4 border-t border-gray-100">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <div className="w-8 sm:w-10 h-8 sm:h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-4 sm:w-5 h-4 sm:h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-xs sm:text-sm font-medium text-gray-900 truncate">João Silva</p>
            <p className="text-xs text-gray-500 truncate">Engenheiro Agrônomo</p>
          </div>
          <Settings className="w-4 sm:w-5 h-4 sm:h-5 text-gray-400 cursor-pointer hover:text-gray-600 flex-shrink-0" />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
