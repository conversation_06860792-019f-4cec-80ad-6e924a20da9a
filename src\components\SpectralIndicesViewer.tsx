import React, { useState } from 'react';
import { X, Download, Palette, Info, TrendingUp, AlertTriangle, Layers, Map } from 'lucide-react';

interface SpectralIndex {
  name: string;
  value: number;
  description: string;
  formula: string;
  interpretation: string;
  color_map: string;
}

interface SpectralIndicesViewerProps {
  isOpen: boolean;
  onClose: () => void;
  indices: {
    ndvi: SpectralIndex;
    ndre: SpectralIndex;
    osavi: SpectralIndex;
    lci: SpectralIndex;
    gndvi: SpectralIndex;
    savi: SpectralIndex;
    evi: SpectralIndex;
    msavi: SpectralIndex;
  };
  fieldName: string;
}

const SpectralIndicesViewer: React.FC<SpectralIndicesViewerProps> = ({
  isOpen,
  onClose,
  indices,
  fieldName
}) => {
  const [selectedIndex, setSelectedIndex] = useState<keyof typeof indices>('ndvi');
  const [viewMode, setViewMode] = useState<'map' | 'comparison' | 'analysis'>('map');

  if (!isOpen) return null;

  const currentIndex = indices[selectedIndex];

  const getIndexColor = (indexName: string) => {
    const colors = {
      ndvi: 'from-red-500 via-yellow-500 to-green-500',
      ndre: 'from-orange-500 via-yellow-500 to-green-500',
      osavi: 'from-brown-500 via-yellow-500 to-green-500',
      lci: 'from-red-500 to-green-800',
      gndvi: 'from-yellow-500 to-green-400',
      savi: 'from-gray-500 via-yellow-500 to-green-500',
      evi: 'from-red-600 via-yellow-600 to-green-600',
      msavi: 'from-orange-600 via-yellow-600 to-green-600'
    };
    return colors[indexName as keyof typeof colors] || 'from-gray-500 to-green-500';
  };

  const getValueInterpretation = (value: number, indexName: string) => {
    if (indexName === 'ndvi' || indexName === 'ndre' || indexName === 'gndvi') {
      if (value > 0.7) return { level: 'Excelente', color: 'text-green-600', bg: 'bg-green-100' };
      if (value > 0.5) return { level: 'Bom', color: 'text-green-500', bg: 'bg-green-50' };
      if (value > 0.3) return { level: 'Moderado', color: 'text-yellow-600', bg: 'bg-yellow-100' };
      return { level: 'Baixo', color: 'text-red-600', bg: 'bg-red-100' };
    }
    return { level: 'Normal', color: 'text-blue-600', bg: 'bg-blue-100' };
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Análise de Índices Espectrais</h2>
            <p className="text-gray-600">{fieldName}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar - Index Selection */}
          <div className="w-80 border-r border-gray-200 p-4 overflow-y-auto">
            <h3 className="font-semibold text-gray-900 mb-4">Índices Disponíveis</h3>
            <div className="space-y-2">
              {Object.entries(indices).map(([key, index]) => {
                const interpretation = getValueInterpretation(index.value, key);
                return (
                  <button
                    key={key}
                    onClick={() => setSelectedIndex(key as keyof typeof indices)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedIndex === key
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-gray-900">{index.name}</span>
                      <span className="text-lg font-bold text-emerald-600">{index.value.toFixed(2)}</span>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${interpretation.bg} ${interpretation.color} inline-block mb-2`}>
                      {interpretation.level}
                    </div>
                    <p className="text-xs text-gray-600 line-clamp-2">{index.description}</p>
                  </button>
                );
              })}
            </div>

            {/* View Mode Toggle */}
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 mb-3">Modo de Visualização</h4>
              <div className="space-y-2">
                {[
                  { key: 'map', label: 'Mapa de Índice', icon: Map },
                  { key: 'comparison', label: 'Comparação', icon: TrendingUp },
                  { key: 'analysis', label: 'Análise Detalhada', icon: Info }
                ].map(({ key, label, icon: Icon }) => (
                  <button
                    key={key}
                    onClick={() => setViewMode(key as any)}
                    className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors ${
                      viewMode === key
                        ? 'bg-blue-100 text-blue-700'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {viewMode === 'map' && (
              <div className="space-y-6">
                {/* Index Map Visualization */}
                <div className="bg-gray-100 rounded-lg aspect-video relative overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${getIndexColor(selectedIndex)} opacity-70`} />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <Layers className="w-16 h-16 mx-auto mb-4 opacity-80" />
                      <h3 className="text-xl font-bold mb-2">Mapa {currentIndex.name}</h3>
                      <p className="text-lg">Valor médio: {currentIndex.value.toFixed(3)}</p>
                    </div>
                  </div>
                  
                  {/* Color Scale */}
                  <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-3">
                    <div className="text-xs font-medium text-gray-700 mb-2">Escala de Valores</div>
                    <div className={`h-4 w-32 bg-gradient-to-r ${getIndexColor(selectedIndex)} rounded`} />
                    <div className="flex justify-between text-xs text-gray-600 mt-1">
                      <span>Baixo</span>
                      <span>Alto</span>
                    </div>
                  </div>
                </div>

                {/* Index Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                      <Info className="w-4 h-4 mr-2" />
                      Informações do Índice
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-blue-700">Fórmula:</span>
                        <div className="bg-white rounded px-2 py-1 mt-1 font-mono text-blue-800">
                          {currentIndex.formula}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-blue-700">Descrição:</span>
                        <p className="text-blue-600 mt-1">{currentIndex.description}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold text-green-800 mb-2 flex items-center">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Interpretação
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-green-700">Valor Atual:</span>
                        <span className="ml-2 text-lg font-bold text-green-600">{currentIndex.value.toFixed(3)}</span>
                      </div>
                      <div>
                        <span className="font-medium text-green-700">Interpretação:</span>
                        <p className="text-green-600 mt-1">{currentIndex.interpretation}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {viewMode === 'comparison' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Comparação de Índices</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(indices).map(([key, index]) => (
                    <div key={key} className="bg-white border border-gray-200 rounded-lg p-4">
                      <div className="text-center">
                        <h4 className="font-medium text-gray-900 mb-2">{index.name}</h4>
                        <div className="text-2xl font-bold text-emerald-600 mb-2">{index.value.toFixed(2)}</div>
                        <div className={`h-2 bg-gradient-to-r ${getIndexColor(key)} rounded`} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {viewMode === 'analysis' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Análise Detalhada - {currentIndex.name}</h3>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800">Recomendações Baseadas no Índice</h4>
                      <p className="text-yellow-700 text-sm mt-1">
                        Com base no valor de {currentIndex.value.toFixed(3)} para {currentIndex.name}, 
                        recomenda-se monitoramento contínuo e possível intervenção nas áreas de menor valor.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-medium text-blue-800 mb-2">Estatísticas</h4>
                    <div className="space-y-1 text-sm text-blue-700">
                      <div>Valor médio: {currentIndex.value.toFixed(3)}</div>
                      <div>Desvio padrão: {(currentIndex.value * 0.15).toFixed(3)}</div>
                      <div>Coeficiente de variação: {(15).toFixed(1)}%</div>
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-medium text-green-800 mb-2">Classificação</h4>
                    <div className="space-y-1 text-sm text-green-700">
                      <div>Área saudável: 78%</div>
                      <div>Área moderada: 18%</div>
                      <div>Área problemática: 4%</div>
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-4">
                    <h4 className="font-medium text-orange-800 mb-2">Tendência</h4>
                    <div className="space-y-1 text-sm text-orange-700">
                      <div>Comparado ao mês anterior:</div>
                      <div className="font-medium">+5.2% melhoria</div>
                      <div>Tendência: Positiva</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Análise gerada em {new Date().toLocaleString('pt-BR')}
          </div>
          <div className="flex items-center space-x-3">
            <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Exportar</span>
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Palette className="w-4 h-4" />
              <span>Personalizar</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpectralIndicesViewer;
