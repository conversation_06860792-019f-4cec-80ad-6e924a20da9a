import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { Tables, TablesInsert } from '../lib/database.types';

type DroneImage = Tables<'drone_images'>;
type DroneImageInsert = TablesInsert<'drone_images'>;

export const useDroneImages = (farmId?: string) => {
  const [images, setImages] = useState<DroneImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchImages = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('drone_images')
        .select('*')
        .order('created_at', { ascending: false });

      if (farmId) {
        query = query.eq('farm_id', farmId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setImages(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar imagens');
    } finally {
      setLoading(false);
    }
  };

  const createImage = async (image: DroneImageInsert) => {
    try {
      const { data, error } = await supabase
        .from('drone_images')
        .insert(image)
        .select()
        .single();

      if (error) throw error;
      setImages(prev => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar imagem';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const updateImageStatus = async (id: string, status: Tables<'drone_images'>['processing_status']) => {
    try {
      const { data, error } = await supabase
        .from('drone_images')
        .update({ processing_status: status })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setImages(prev => prev.map(img => img.id === id ? data : img));
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar status';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const deleteImage = async (id: string) => {
    try {
      const { error } = await supabase
        .from('drone_images')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setImages(prev => prev.filter(img => img.id !== id));
      return { error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar imagem';
      setError(errorMessage);
      return { error: errorMessage };
    }
  };

  useEffect(() => {
    fetchImages();
  }, [farmId]);

  return {
    images,
    loading,
    error,
    createImage,
    updateImageStatus,
    deleteImage,
    refetch: fetchImages
  };
};