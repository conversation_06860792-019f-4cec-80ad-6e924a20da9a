import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { Tables, TablesInsert } from '../lib/database.types';

type DroneImage = Tables<'drone_images'>;
type DroneImageInsert = TablesInsert<'drone_images'>;

export const useDroneImages = (farmId?: string) => {
  const [images, setImages] = useState<DroneImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchImages = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('drone_images')
        .select('*')
        .order('created_at', { ascending: false });

      if (farmId) {
        query = query.eq('farm_id', parseInt(farmId));
      }

      const { data, error } = await query;

      if (error) throw error;
      setImages(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar imagens');
    } finally {
      setLoading(false);
    }
  };

  const createImage = async (image: DroneImageInsert) => {
    try {
      const { data, error } = await supabase
        .from('drone_images')
        .insert(image)
        .select()
        .single();

      if (error) throw error;
      setImages(prev => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar imagem';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const updateImageStatus = async (id: string, status: Tables<'drone_images'>['processing_status']) => {
    try {
      const numericId = parseInt(id);
      const { data, error } = await supabase
        .from('drone_images')
        .update({ processing_status: status })
        .eq('id', numericId)
        .select()
        .single();

      if (error) throw error;
      setImages(prev => prev.map(img => img.id === numericId ? data : img));
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar status';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const uploadImage = async (file: File, farmId: string, metadata: Partial<DroneImageInsert> = {}) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      // Por enquanto, vamos simular o upload sem usar storage
      // Em produção, você integraria com um serviço de storage real
      const fileName = file.name;
      const imageType = detectImageType(file.name);

      // Salvar metadados no banco
      const imageData: DroneImageInsert = {
        farm_id: parseInt(farmId),
        filename: fileName,
        original_filename: file.name,
        image_type: imageType,
        processing_status: 'pending',
        ...metadata
      };

      const { data, error } = await supabase
        .from('drone_images')
        .insert(imageData)
        .select()
        .single();

      if (error) throw error;

      setImages(prev => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao fazer upload';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const analyzeImage = async (imageId: string) => {
    try {
      const numericId = parseInt(imageId);

      // Atualizar status para processando
      await updateImageStatus(imageId, 'processing');

      // Simular análise (aqui você integraria com serviços de IA reais)
      const analysisResults = await simulateAnalysis();

      // Atualizar com resultados da análise
      const { data, error } = await supabase
        .from('drone_images')
        .update({
          processing_status: 'completed',
          analysis_results: analysisResults
        })
        .eq('id', numericId)
        .select()
        .single();

      if (error) throw error;

      setImages(prev => prev.map(img =>
        img.id === numericId ? data : img
      ));

      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro na análise';

      // Atualizar status para erro
      await updateImageStatus(imageId, 'failed');
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const deleteImage = async (id: string) => {
    try {
      const image = images.find(img => img.id === id);
      if (!image) throw new Error('Imagem não encontrada');

      // Deletar arquivo do storage se existir
      if (image.filename && image.farm_id) {
        const filePath = `drone-images/${image.farm_id}/${image.filename}`;
        await supabase.storage
          .from('drone-images')
          .remove([filePath]);
      }

      // Deletar registro do banco
      const { error } = await supabase
        .from('drone_images')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setImages(prev => prev.filter(img => img.id !== id));
      return { error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar imagem';
      setError(errorMessage);
      return { error: errorMessage };
    }
  };

  useEffect(() => {
    fetchImages();
  }, [farmId]);

  return {
    images,
    loading,
    error,
    createImage,
    updateImageStatus,
    uploadImage,
    analyzeImage,
    deleteImage,
    refetch: fetchImages
  };
};

// Função auxiliar para detectar tipo de imagem
const detectImageType = (filename: string): 'rgb' | 'nir' | 'multispectral' | 'thermal' => {
  const name = filename.toLowerCase();
  if (name.includes('nir') || name.includes('infrared')) return 'nir';
  if (name.includes('multi') || name.includes('spectral')) return 'multispectral';
  if (name.includes('thermal') || name.includes('temp')) return 'thermal';
  return 'rgb';
};

// Função para simular análise (substituir por integração real)
const simulateAnalysis = async () => {
  // Simular delay de processamento
  await new Promise(resolve => setTimeout(resolve, 3000));

  return {
    ndvi_analysis: {
      success: true,
      mean_ndvi: 0.75 + (Math.random() - 0.5) * 0.3,
      vegetation_percentage: 85 + Math.random() * 10,
      healthy_vegetation_percentage: 80 + Math.random() * 15,
      stress_areas: [],
      growth_stage: 'vegetative',
      biomass_estimate: 4.2 + (Math.random() - 0.5) * 1.0
    },
    pest_detection: {
      success: true,
      total_detections: Math.floor(Math.random() * 5),
      pests_found: [],
      risk_level: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
    },
    disease_analysis: {
      success: true,
      diseases_detected: [],
      overall_health_score: 85 + Math.random() * 10,
      infection_risk: Math.random() > 0.8 ? 'high' : Math.random() > 0.5 ? 'medium' : 'low'
    },
    multispectral_analysis: {
      indices: {
        ndvi: {
          name: 'NDVI',
          value: 0.75 + (Math.random() - 0.5) * 0.3,
          description: 'Índice de vegetação mais comumente usado que fornece informações sobre o teor de clorofila das plantas.',
          formula: '(Nir-Red)/(Nir+Red)',
          interpretation: 'Vegetação saudável com boa atividade fotossintética',
          color_map: 'green-red'
        },
        ndre: {
          name: 'NDRE',
          value: 0.42 + (Math.random() - 0.5) * 0.2,
          description: 'A pesquisa mostrou que este índice fornece informações sobre o teor de clorofila das culturas mais tarde na estação de crescimento.',
          formula: '(Nir-RedEdge)/(Nir+ RedEdge)',
          interpretation: 'Indica estágio avançado de crescimento com boa saúde',
          color_map: 'green-yellow'
        }
      },
      vegetation_health: {
        overall_score: 85 + Math.random() * 10,
        chlorophyll_content: 'high',
        biomass_estimate: 4.2 + (Math.random() - 0.5) * 1.0,
        growth_stage: 'vegetative',
        stress_indicators: ['Leve estresse hídrico na zona norte', 'Deficiência nutricional pontual']
      },
      spatial_analysis: {
        uniform_areas: 78 + Math.random() * 10,
        variable_areas: 22 - Math.random() * 10,
        problem_zones: [
          {
            coordinates: { x: 150, y: 200 },
            area_hectares: 2.3,
            issue_type: 'Estresse hídrico',
            severity: 'medium'
          }
        ]
      }
    }
  };
};