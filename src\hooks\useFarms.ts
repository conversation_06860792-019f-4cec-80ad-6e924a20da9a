import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { Tables, TablesInsert, TablesUpdate } from '../lib/database.types';

type Farm = Tables<'farms'>;
type FarmInsert = TablesInsert<'farms'>;
type FarmUpdate = TablesUpdate<'farms'>;

export const useFarms = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFarms = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('farms')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFarms(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar fazendas');
    } finally {
      setLoading(false);
    }
  };

  const createFarm = async (farm: Omit<FarmInsert, 'user_id'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Usuário não autenticado');

      const { data, error } = await supabase
        .from('farms')
        .insert({ ...farm, user_id: user.id })
        .select()
        .single();

      if (error) throw error;
      setFarms(prev => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar fazenda';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const updateFarm = async (id: string, updates: FarmUpdate) => {
    try {
      const { data, error } = await supabase
        .from('farms')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      setFarms(prev => prev.map(farm => farm.id === id ? data : farm));
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar fazenda';
      setError(errorMessage);
      return { data: null, error: errorMessage };
    }
  };

  const deleteFarm = async (id: string) => {
    try {
      const { error } = await supabase
        .from('farms')
        .delete()
        .eq('id', id);

      if (error) throw error;
      setFarms(prev => prev.filter(farm => farm.id !== id));
      return { error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar fazenda';
      setError(errorMessage);
      return { error: errorMessage };
    }
  };

  useEffect(() => {
    fetchFarms();
  }, []);

  return {
    farms,
    loading,
    error,
    createFarm,
    updateFarm,
    deleteFarm,
    refetch: fetchFarms
  };
};