export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      disease_detections: {
        Row: {
          affected_area_percentage: number
          confidence: number
          created_at: string | null
          disease_name: string
          drone_image_id: number | null
          id: number
          location_zones: Json | null
          severity: Database["public"]["Enums"]["disease_severity"]
          treatment_recommendation: string | null
        }
        Insert: {
          affected_area_percentage: number
          confidence: number
          created_at?: string | null
          disease_name: string
          drone_image_id?: number | null
          id?: number
          location_zones?: Json | null
          severity: Database["public"]["Enums"]["disease_severity"]
          treatment_recommendation?: string | null
        }
        Update: {
          affected_area_percentage?: number
          confidence?: number
          created_at?: string | null
          disease_name?: string
          drone_image_id?: number | null
          id?: number
          location_zones?: Json | null
          severity?: Database["public"]["Enums"]["disease_severity"]
          treatment_recommendation?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "disease_detections_drone_image_id_fkey"
            columns: ["drone_image_id"]
            isOneToOne: false
            referencedRelation: "drone_images"
            referencedColumns: ["id"]
          },
        ]
      }
      drone_images: {
        Row: {
          altitude: number | null
          analysis_results: Json | null
          capture_date: string | null
          created_at: string | null
          farm_id: number | null
          field_id: number | null
          filename: string
          id: number
          image_type: Database["public"]["Enums"]["image_type"]
          latitude: number | null
          longitude: number | null
          original_filename: string
          processing_status: Database["public"]["Enums"]["processing_status"] | null
          weather_conditions: string | null
        }
        Insert: {
          altitude?: number | null
          analysis_results?: Json | null
          capture_date?: string | null
          created_at?: string | null
          farm_id?: number | null
          field_id?: number | null
          filename: string
          id?: number
          image_type: Database["public"]["Enums"]["image_type"]
          latitude?: number | null
          longitude?: number | null
          original_filename: string
          processing_status?: Database["public"]["Enums"]["processing_status"] | null
          weather_conditions?: string | null
        }
        Update: {
          altitude?: number | null
          analysis_results?: Json | null
          capture_date?: string | null
          created_at?: string | null
          farm_id?: number | null
          field_id?: number | null
          filename?: string
          id?: number
          image_type?: Database["public"]["Enums"]["image_type"]
          latitude?: number | null
          longitude?: number | null
          original_filename?: string
          processing_status?: Database["public"]["Enums"]["processing_status"] | null
          weather_conditions?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "drone_images_farm_id_fkey"
            columns: ["farm_id"]
            isOneToOne: false
            referencedRelation: "farms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "drone_images_field_id_fkey"
            columns: ["field_id"]
            isOneToOne: false
            referencedRelation: "fields"
            referencedColumns: ["id"]
          },
        ]
      }
      farms: {
        Row: {
          address: string
          created_at: string | null
          crop_type: string
          id: number
          latitude: number
          longitude: number
          name: string
          owner_contact: string | null
          owner_name: string
          total_area: number
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          address: string
          created_at?: string | null
          crop_type: string
          id?: number
          latitude: number
          longitude: number
          name: string
          owner_contact?: string | null
          owner_name: string
          total_area: number
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          address?: string
          created_at?: string | null
          crop_type?: string
          id?: number
          latitude?: number
          longitude?: number
          name?: string
          owner_contact?: string | null
          owner_name?: string
          total_area?: number
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      fields: {
        Row: {
          area: number
          coordinates: string | null
          created_at: string | null
          crop_type: string | null
          farm_id: number | null
          id: number
          name: string
        }
        Insert: {
          area: number
          coordinates?: string | null
          created_at?: string | null
          crop_type?: string | null
          farm_id?: number | null
          id?: number
          name: string
        }
        Update: {
          area?: number
          coordinates?: string | null
          created_at?: string | null
          crop_type?: string | null
          farm_id?: number | null
          id?: number
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "fields_farm_id_fkey"
            columns: ["farm_id"]
            isOneToOne: false
            referencedRelation: "farms"
            referencedColumns: ["id"]
          },
        ]
      }
      ndvi_analysis: {
        Row: {
          biomass_estimate: number | null
          created_at: string | null
          drone_image_id: number | null
          growth_stage: string | null
          healthy_vegetation_percentage: number | null
          id: number
          max_ndvi: number
          mean_ndvi: number
          min_ndvi: number
          stress_areas: Json | null
          vegetation_percentage: number | null
        }
        Insert: {
          biomass_estimate?: number | null
          created_at?: string | null
          drone_image_id?: number | null
          growth_stage?: string | null
          healthy_vegetation_percentage?: number | null
          id?: number
          max_ndvi: number
          mean_ndvi: number
          min_ndvi: number
          stress_areas?: Json | null
          vegetation_percentage?: number | null
        }
        Update: {
          biomass_estimate?: number | null
          created_at?: string | null
          drone_image_id?: number | null
          growth_stage?: string | null
          healthy_vegetation_percentage?: number | null
          id?: number
          max_ndvi?: number
          mean_ndvi?: number
          min_ndvi?: number
          stress_areas?: Json | null
          vegetation_percentage?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "ndvi_analysis_drone_image_id_fkey"
            columns: ["drone_image_id"]
            isOneToOne: false
            referencedRelation: "drone_images"
            referencedColumns: ["id"]
          },
        ]
      }
      pest_detections: {
        Row: {
          confidence: number
          created_at: string | null
          drone_image_id: number | null
          id: number
          location_data: Json
          pest_type: string
          recommended_action: string | null
          severity: Database["public"]["Enums"]["severity_level"]
        }
        Insert: {
          confidence: number
          created_at?: string | null
          drone_image_id?: number | null
          id?: number
          location_data: Json
          pest_type: string
          recommended_action?: string | null
          severity: Database["public"]["Enums"]["severity_level"]
        }
        Update: {
          confidence?: number
          created_at?: string | null
          drone_image_id?: number | null
          id?: number
          location_data?: Json
          pest_type?: string
          recommended_action?: string | null
          severity?: Database["public"]["Enums"]["severity_level"]
        }
        Relationships: [
          {
            foreignKeyName: "pest_detections_drone_image_id_fkey"
            columns: ["drone_image_id"]
            isOneToOne: false
            referencedRelation: "drone_images"
            referencedColumns: ["id"]
          },
        ]
      }
      reports: {
        Row: {
          created_at: string | null
          data: Json | null
          description: string | null
          farm_id: number | null
          file_url: string | null
          id: number
          period_end: string | null
          period_start: string | null
          report_type: Database["public"]["Enums"]["report_type"]
          status: Database["public"]["Enums"]["report_status"] | null
          title: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          description?: string | null
          farm_id?: number | null
          file_url?: string | null
          id?: number
          period_end?: string | null
          period_start?: string | null
          report_type: Database["public"]["Enums"]["report_type"]
          status?: Database["public"]["Enums"]["report_status"] | null
          title: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          description?: string | null
          farm_id?: number | null
          file_url?: string | null
          id?: number
          period_end?: string | null
          period_start?: string | null
          report_type?: Database["public"]["Enums"]["report_type"]
          status?: Database["public"]["Enums"]["report_status"] | null
          title?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reports_farm_id_fkey"
            columns: ["farm_id"]
            isOneToOne: false
            referencedRelation: "farms"
            referencedColumns: ["id"]
          },
        ]
      }
      spray_recommendations: {
        Row: {
          application_sequence: Json | null
          created_at: string | null
          drone_image_id: number | null
          estimated_savings: number | null
          farm_id: number | null
          id: number
          status: Database["public"]["Enums"]["spray_status"] | null
          total_area: number
          zones: Json
        }
        Insert: {
          application_sequence?: Json | null
          created_at?: string | null
          drone_image_id?: number | null
          estimated_savings?: number | null
          farm_id?: number | null
          id?: number
          status?: Database["public"]["Enums"]["spray_status"] | null
          total_area: number
          zones: Json
        }
        Update: {
          application_sequence?: Json | null
          created_at?: string | null
          drone_image_id?: number | null
          estimated_savings?: number | null
          farm_id?: number | null
          id?: number
          status?: Database["public"]["Enums"]["spray_status"] | null
          total_area?: number
          zones?: Json
        }
        Relationships: [
          {
            foreignKeyName: "spray_recommendations_drone_image_id_fkey"
            columns: ["drone_image_id"]
            isOneToOne: false
            referencedRelation: "drone_images"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "spray_recommendations_farm_id_fkey"
            columns: ["farm_id"]
            isOneToOne: false
            referencedRelation: "farms"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      disease_severity: "early" | "moderate" | "severe"
      image_type: "rgb" | "nir" | "multispectral" | "thermal"
      processing_status: "pending" | "processing" | "completed" | "failed"
      report_status: "generating" | "completed" | "failed"
      report_type: "ndvi" | "productivity" | "anomalies" | "water" | "weather" | "custom"
      severity_level: "low" | "medium" | "high"
      spray_status: "pending" | "approved" | "applied" | "completed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"] & Database["public"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] &
        Database["public"]["Views"])
    ? (Database["public"]["Tables"] &
        Database["public"]["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
    ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
    ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof Database["public"]["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof Database["public"]["Enums"]
    ? Database["public"]["Enums"][PublicEnumNameOrOptions]
    : never