/*
  # Create custom enums for the agriculture system

  1. Enums
    - `image_type` - Types of drone images (rgb, nir, multispectral, thermal)
    - `processing_status` - Status of image processing (pending, processing, completed, failed)
    - `severity_level` - Severity levels for pest detection (low, medium, high)
    - `disease_severity` - Disease severity levels (early, moderate, severe)
    - `report_type` - Types of reports (ndvi, productivity, anomalies, water, weather, custom)
    - `report_status` - Report generation status (generating, completed, failed)
    - `spray_status` - Spray recommendation status (pending, approved, applied, completed)
*/

-- Create image_type enum
CREATE TYPE image_type AS ENUM ('rgb', 'nir', 'multispectral', 'thermal');

-- Create processing_status enum
CREATE TYPE processing_status AS ENUM ('pending', 'processing', 'completed', 'failed');

-- Create severity_level enum
CREATE TYPE severity_level AS ENUM ('low', 'medium', 'high');

-- Create disease_severity enum
CREATE TYPE disease_severity AS ENUM ('early', 'moderate', 'severe');

-- Create report_type enum
CREATE TYPE report_type AS ENUM ('ndvi', 'productivity', 'anomalies', 'water', 'weather', 'custom');

-- Create report_status enum
CREATE TYPE report_status AS ENUM ('generating', 'completed', 'failed');

-- Create spray_status enum
CREATE TYPE spray_status AS ENUM ('pending', 'approved', 'applied', 'completed');