/*
  # Create farms table

  1. New Tables
    - `farms`
      - `id` (uuid, primary key)
      - `name` (text, not null)
      - `address` (text, not null)
      - `latitude` (numeric, not null)
      - `longitude` (numeric, not null)
      - `total_area` (numeric, not null)
      - `crop_type` (text, not null)
      - `owner_name` (text, not null)
      - `owner_contact` (text, optional)
      - `user_id` (uuid, foreign key to users)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `farms` table
    - Add policies for users to manage their own farms

  3. Indexes
    - Index on user_id for performance
    - Index on location coordinates
*/

CREATE TABLE IF NOT EXISTS farms (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  address text NOT NULL,
  latitude numeric(10,8) NOT NULL,
  longitude numeric(11,8) NOT NULL,
  total_area numeric(10,2) NOT NULL,
  crop_type text NOT NULL,
  owner_name text NOT NULL,
  owner_contact text,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE farms ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Usuários podem ver suas próprias fazendas"
  ON farms
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir suas próprias fazendas"
  ON farms
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar suas próprias fazendas"
  ON farms
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem deletar suas próprias fazendas"
  ON farms
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_farms_user_id ON farms(user_id);
CREATE INDEX IF NOT EXISTS idx_farms_location ON farms(latitude, longitude);

-- Create trigger for updated_at
CREATE TRIGGER update_farms_updated_at
  BEFORE UPDATE ON farms
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();