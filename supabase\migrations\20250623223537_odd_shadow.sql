/*
  # Create fields table for farm subdivisions

  1. New Tables
    - `fields`
      - `id` (uuid, primary key)
      - `farm_id` (uuid, foreign key to farms)
      - `name` (text, not null)
      - `area` (numeric, not null)
      - `crop_type` (text, optional)
      - `coordinates` (text, optional - JSO<PERSON> string for polygon coordinates)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `fields` table
    - Add policies for users to manage fields of their farms

  3. Indexes
    - Index on farm_id for performance
*/

CREATE TABLE IF NOT EXISTS fields (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id uuid REFERENCES farms(id) ON DELETE CASCADE,
  name text NOT NULL,
  area numeric(10,2) NOT NULL,
  crop_type text,
  coordinates text, -- JSON string for polygon coordinates
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE fields ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Usu<PERSON><PERSON>s podem ver talhões de suas fazendas"
  ON fields
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = fields.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir talhões em suas fazendas"
  ON fields
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = fields.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem atualizar talhões de suas fazendas"
  ON fields
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = fields.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem deletar talhões de suas fazendas"
  ON fields
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = fields.farm_id
      AND farms.user_id = auth.uid()
    )
  );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_fields_farm_id ON fields(farm_id);