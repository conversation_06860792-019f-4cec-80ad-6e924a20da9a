/*
  # Create drone images table

  1. New Tables
    - `drone_images`
      - `id` (uuid, primary key)
      - `farm_id` (uuid, foreign key to farms)
      - `field_id` (uuid, foreign key to fields, optional)
      - `filename` (text, not null)
      - `original_filename` (text, not null)
      - `image_type` (image_type enum, not null)
      - `latitude` (numeric, optional)
      - `longitude` (numeric, optional)
      - `altitude` (numeric, optional)
      - `capture_date` (timestamp, optional)
      - `weather_conditions` (text, optional)
      - `processing_status` (processing_status enum, default pending)
      - `analysis_results` (jsonb, default empty object)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `drone_images` table
    - Add policies for users to manage images of their farms

  3. Indexes
    - Index on farm_id and field_id for performance
    - Index on capture_date for time-based queries
    - Index on location coordinates
*/

CREATE TABLE IF NOT EXISTS drone_images (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id uuid REFERENCES farms(id) ON DELETE CASCADE,
  field_id uuid REFERENCES fields(id) ON DELETE SET NULL,
  filename text NOT NULL,
  original_filename text NOT NULL,
  image_type image_type NOT NULL,
  latitude numeric(10,8),
  longitude numeric(11,8),
  altitude numeric(8,2),
  capture_date timestamptz,
  weather_conditions text,
  processing_status processing_status DEFAULT 'pending',
  analysis_results jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE drone_images ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Usuários podem ver imagens de suas fazendas"
  ON drone_images
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = drone_images.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir imagens em suas fazendas"
  ON drone_images
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = drone_images.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem atualizar imagens de suas fazendas"
  ON drone_images
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = drone_images.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem deletar imagens de suas fazendas"
  ON drone_images
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = drone_images.farm_id
      AND farms.user_id = auth.uid()
    )
  );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_drone_images_farm_id ON drone_images(farm_id);
CREATE INDEX IF NOT EXISTS idx_drone_images_field_id ON drone_images(field_id);
CREATE INDEX IF NOT EXISTS idx_drone_images_capture_date ON drone_images(capture_date);
CREATE INDEX IF NOT EXISTS idx_drone_images_location ON drone_images(latitude, longitude);