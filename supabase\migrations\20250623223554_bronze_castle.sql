/*
  # Create analysis tables for drone image processing results

  1. New Tables
    - `ndvi_analysis` - NDVI analysis results
    - `pest_detections` - Pest detection results
    - `disease_detections` - Disease detection results
    - `spray_recommendations` - Spray recommendations

  2. Security
    - Enable RLS on all tables
    - Add policies for users to access analysis of their images

  3. Indexes
    - Index on drone_image_id for performance
*/

-- NDVI Analysis table
CREATE TABLE IF NOT EXISTS ndvi_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  drone_image_id uuid REFERENCES drone_images(id) ON DELETE CASCADE,
  mean_ndvi numeric(4,3) NOT NULL,
  min_ndvi numeric(4,3) NOT NULL,
  max_ndvi numeric(4,3) NOT NULL,
  vegetation_percentage numeric(5,2) DEFAULT 0,
  healthy_vegetation_percentage numeric(5,2) DEFAULT 0,
  stress_areas jsonb DEFAULT '[]',
  growth_stage text,
  biomass_estimate numeric(10,2),
  created_at timestamptz DEFAULT now()
);

-- Pest Detections table
CREATE TABLE IF NOT EXISTS pest_detections (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  drone_image_id uuid REFERENCES drone_images(id) ON DELETE CASCADE,
  pest_type text NOT NULL,
  confidence numeric(4,3) NOT NULL,
  location_data jsonb NOT NULL,
  severity severity_level NOT NULL,
  recommended_action text,
  created_at timestamptz DEFAULT now()
);

-- Disease Detections table
CREATE TABLE IF NOT EXISTS disease_detections (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  drone_image_id uuid REFERENCES drone_images(id) ON DELETE CASCADE,
  disease_name text NOT NULL,
  confidence numeric(4,3) NOT NULL,
  affected_area_percentage numeric(5,2) NOT NULL,
  severity disease_severity NOT NULL,
  location_zones jsonb DEFAULT '[]',
  treatment_recommendation text,
  created_at timestamptz DEFAULT now()
);

-- Spray Recommendations table
CREATE TABLE IF NOT EXISTS spray_recommendations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id uuid REFERENCES farms(id) ON DELETE CASCADE,
  drone_image_id uuid REFERENCES drone_images(id) ON DELETE SET NULL,
  zones jsonb NOT NULL,
  total_area numeric(10,2) NOT NULL,
  estimated_savings numeric(10,2) DEFAULT 0,
  application_sequence jsonb DEFAULT '[]',
  status spray_status DEFAULT 'pending',
  created_at timestamptz DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE ndvi_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE pest_detections ENABLE ROW LEVEL SECURITY;
ALTER TABLE disease_detections ENABLE ROW LEVEL SECURITY;
ALTER TABLE spray_recommendations ENABLE ROW LEVEL SECURITY;

-- NDVI Analysis policies
CREATE POLICY "Usuários podem ver análises NDVI de suas imagens"
  ON ndvi_analysis
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = ndvi_analysis.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir análises NDVI em suas imagens"
  ON ndvi_analysis
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = ndvi_analysis.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

-- Pest Detections policies
CREATE POLICY "Usuários podem ver detecções de pragas de suas imagens"
  ON pest_detections
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = pest_detections.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir detecções de pragas em suas imagens"
  ON pest_detections
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = pest_detections.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

-- Disease Detections policies
CREATE POLICY "Usuários podem ver detecções de doenças de suas imagens"
  ON disease_detections
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = disease_detections.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir detecções de doenças em suas imagens"
  ON disease_detections
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM drone_images
      JOIN farms ON farms.id = drone_images.farm_id
      WHERE drone_images.id = disease_detections.drone_image_id
      AND farms.user_id = auth.uid()
    )
  );

-- Spray Recommendations policies
CREATE POLICY "Usuários podem ver recomendações de suas fazendas"
  ON spray_recommendations
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = spray_recommendations.farm_id
      AND farms.user_id = auth.uid()
    )
  );

CREATE POLICY "Usuários podem inserir recomendações em suas fazendas"
  ON spray_recommendations
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM farms
      WHERE farms.id = spray_recommendations.farm_id
      AND farms.user_id = auth.uid()
    )
  );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_ndvi_analysis_drone_image_id ON ndvi_analysis(drone_image_id);
CREATE INDEX IF NOT EXISTS idx_pest_detections_drone_image_id ON pest_detections(drone_image_id);
CREATE INDEX IF NOT EXISTS idx_disease_detections_drone_image_id ON disease_detections(drone_image_id);
CREATE INDEX IF NOT EXISTS idx_spray_recommendations_farm_id ON spray_recommendations(farm_id);