/*
  # Create reports table

  1. New Tables
    - `reports`
      - `id` (uuid, primary key)
      - `farm_id` (uuid, foreign key to farms, optional)
      - `user_id` (uuid, foreign key to users)
      - `title` (text, not null)
      - `description` (text, optional)
      - `report_type` (report_type enum, not null)
      - `period_start` (date, optional)
      - `period_end` (date, optional)
      - `data` (jsonb, default empty object)
      - `file_url` (text, optional)
      - `status` (report_status enum, default generating)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `reports` table
    - Add policies for users to manage their own reports

  3. Indexes
    - Index on user_id and farm_id for performance
    - Index on created_at for time-based queries
*/

CREATE TABLE IF NOT EXISTS reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id uuid REFERENCES farms(id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  report_type report_type NOT NULL,
  period_start date,
  period_end date,
  data jsonb DEFAULT '{}',
  file_url text,
  status report_status DEFAULT 'generating',
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Usuários podem ver seus próprios relatórios"
  ON reports
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir seus próprios relatórios"
  ON reports
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar seus próprios relatórios"
  ON reports
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem deletar seus próprios relatórios"
  ON reports
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id);
CREATE INDEX IF NOT EXISTS idx_reports_farm_id ON reports(farm_id);
CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at);