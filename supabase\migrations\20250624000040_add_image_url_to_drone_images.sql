-- Add image_url column to drone_images table
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_url text;

-- Add updated_at column for tracking changes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_drone_images_updated_at
    BEFORE UPDATE ON drone_images
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();