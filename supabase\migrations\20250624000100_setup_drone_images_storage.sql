-- Create storage bucket for drone images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'drone-images',
  'drone-images',
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/tiff', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view drone images from their farms
CREATE POLICY "Users can view drone images from their farms"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'drone-images' AND
  EXISTS (
    SELECT 1 FROM drone_images di
    JOIN farms f ON f.id = di.farm_id
    WHERE di.filename = (storage.foldername(name))[2] || '/' || (storage.filename(name))
    AND f.user_id = auth.uid()
  )
);

-- Policy: Users can upload drone images to their farms
CREATE POLICY "Users can upload drone images to their farms"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'drone-images' AND
  EXISTS (
    SELECT 1 FROM farms f
    WHERE f.id::text = (storage.foldername(name))[1]
    AND f.user_id = auth.uid()
  )
);

-- Policy: Users can update drone images from their farms
CREATE POLICY "Users can update drone images from their farms"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'drone-images' AND
  EXISTS (
    SELECT 1 FROM drone_images di
    JOIN farms f ON f.id = di.farm_id
    WHERE di.filename = (storage.foldername(name))[2] || '/' || (storage.filename(name))
    AND f.user_id = auth.uid()
  )
);

-- Policy: Users can delete drone images from their farms
CREATE POLICY "Users can delete drone images from their farms"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'drone-images' AND
  EXISTS (
    SELECT 1 FROM drone_images di
    JOIN farms f ON f.id = di.farm_id
    WHERE di.filename = (storage.foldername(name))[2] || '/' || (storage.filename(name))
    AND f.user_id = auth.uid()
  )
);

-- Add image_url column to drone_images table
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_url text;

-- Add updated_at column for tracking changes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_drone_images_updated_at 
    BEFORE UPDATE ON drone_images 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to get storage path for drone image
CREATE OR REPLACE FUNCTION get_drone_image_storage_path(farm_id_param integer, filename_param text)
RETURNS text
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN 'drone-images/' || farm_id_param::text || '/' || filename_param;
END;
$$;

-- Create function to get public URL for drone image
CREATE OR REPLACE FUNCTION get_drone_image_public_url(farm_id_param integer, filename_param text)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    base_url text;
    storage_path text;
BEGIN
    -- Get the Supabase project URL (you'll need to replace this with your actual URL)
    base_url := 'https://uoeqlticflhaagkbajnu.supabase.co/storage/v1/object/public/';
    storage_path := get_drone_image_storage_path(farm_id_param, filename_param);
    
    RETURN base_url || storage_path;
END;
$$;
