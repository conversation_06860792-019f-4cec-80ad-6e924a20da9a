-- Note: Storage bucket and policies need to be created via Supabase Dashboard
-- Go to Storage > Create new bucket > Name: "drone-images" > Public: true
-- Then create policies in the Storage section of the dashboard

-- Add image_url column to drone_images table
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_url text;

-- Add updated_at column for tracking changes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_drone_images_updated_at 
    BEFORE UPDATE ON drone_images 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to get storage path for drone image
CREATE OR REPLACE FUNCTION get_drone_image_storage_path(farm_id_param integer, filename_param text)
RETURNS text
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN 'drone-images/' || farm_id_param::text || '/' || filename_param;
END;
$$;

-- Create function to get public URL for drone image
CREATE OR REPLACE FUNCTION get_drone_image_public_url(farm_id_param integer, filename_param text)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    base_url text;
    storage_path text;
BEGIN
    -- Get the Supabase project URL (you'll need to replace this with your actual URL)
    base_url := 'https://uoeqlticflhaagkbajnu.supabase.co/storage/v1/object/public/';
    storage_path := get_drone_image_storage_path(farm_id_param, filename_param);
    
    RETURN base_url || storage_path;
END;
$$;
