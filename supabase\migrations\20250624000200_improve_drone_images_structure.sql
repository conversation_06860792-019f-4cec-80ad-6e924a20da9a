-- Add field_name column to drone_images for better organization
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS field_name text;

-- Add file_size column to track uploaded file sizes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS file_size bigint;

-- Add metadata columns for better image management
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_width integer;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_height integer;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS camera_model text;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS drone_model text;

-- Create index on image_url for faster lookups
CREATE INDEX IF NOT EXISTS idx_drone_images_image_url ON drone_images(image_url);

-- Create index on field_name for filtering
CREATE INDEX IF NOT EXISTS idx_drone_images_field_name ON drone_images(field_name);

-- Create index on file_size for storage management
CREATE INDEX IF NOT EXISTS idx_drone_images_file_size ON drone_images(file_size);

-- Create view for drone images with farm information
CREATE OR REPLACE VIEW drone_images_with_farm AS
SELECT 
    di.*,
    f.name as farm_name,
    f.owner_name as farm_owner,
    f.address as farm_address,
    f.crop_type as farm_crop_type,
    f.total_area as farm_total_area,
    f.user_id as farm_user_id
FROM drone_images di
JOIN farms f ON f.id = di.farm_id;

-- Grant access to the view
GRANT SELECT ON drone_images_with_farm TO authenticated;

-- Create RLS policy for the view
CREATE POLICY "Users can view drone images with farm info from their farms"
ON drone_images_with_farm FOR SELECT
TO authenticated
USING (farm_user_id = auth.uid());

-- Create function to calculate storage usage per farm
CREATE OR REPLACE FUNCTION get_farm_storage_usage(farm_id_param integer)
RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_size bigint;
BEGIN
    SELECT COALESCE(SUM(file_size), 0)
    INTO total_size
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN total_size;
END;
$$;

-- Create function to get image count per farm
CREATE OR REPLACE FUNCTION get_farm_image_count(farm_id_param integer)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_count integer;
BEGIN
    SELECT COUNT(*)
    INTO image_count
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN image_count;
END;
$$;

-- Create function to clean up orphaned storage files
CREATE OR REPLACE FUNCTION cleanup_orphaned_drone_images()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- This function would be used to clean up storage files
    -- that don't have corresponding database records
    -- Implementation would depend on your cleanup strategy
    RAISE NOTICE 'Cleanup function called - implement storage cleanup logic here';
END;
$$;

-- Create function to generate unique filename
CREATE OR REPLACE FUNCTION generate_unique_filename(original_filename text)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    file_extension text;
    base_name text;
    unique_name text;
BEGIN
    -- Extract file extension
    file_extension := LOWER(RIGHT(original_filename, 4));
    
    -- Generate unique filename with timestamp and random string
    unique_name := EXTRACT(EPOCH FROM NOW())::bigint::text || '_' || 
                   SUBSTR(MD5(RANDOM()::text), 1, 8) || file_extension;
    
    RETURN unique_name;
END;
$$;

-- Update existing records to have field_name if null
UPDATE drone_images 
SET field_name = 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int
WHERE field_name IS NULL;

-- Add constraint to ensure field_name is not null for new records
ALTER TABLE drone_images ALTER COLUMN field_name SET DEFAULT 'Talhão A1';

-- Create trigger to auto-generate field_name if not provided
CREATE OR REPLACE FUNCTION auto_generate_field_name()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.field_name IS NULL OR NEW.field_name = '' THEN
        NEW.field_name := 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER auto_generate_field_name_trigger
    BEFORE INSERT ON drone_images
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_field_name();
