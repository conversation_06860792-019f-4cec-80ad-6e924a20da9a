-- Note: Storage policies need to be created via Supabase Dashboard
-- Go to Storage > drone-images bucket > Policies
-- Create the following policies manually:

/*
Policy Name: "Users can view drone images from their farms"
Operation: SELECT
Target roles: authenticated
USING expression:
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)

Policy Name: "Users can upload drone images to their farms"
Operation: INSERT
Target roles: authenticated
WITH CHECK expression:
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)

Policy Name: "Users can update drone images from their farms"
Operation: UPDATE
Target roles: authenticated
USING expression:
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)

Policy Name: "Users can delete drone images from their farms"
Operation: DELETE
Target roles: authenticated
USING expression:
bucket_id = 'drone-images' AND
EXISTS (
  SELECT 1 FROM farms f
  WHERE f.id::text = (string_to_array(name, '/'))[1]
  AND f.user_id = auth.uid()
)
*/

-- Create function to get storage stats for a farm
CREATE OR REPLACE FUNCTION get_farm_storage_stats(farm_id_param integer)
RETURNS TABLE(
  total_images integer,
  total_size_bytes bigint,
  total_size_mb numeric,
  avg_file_size_mb numeric,
  pending_count integer,
  processing_count integer,
  completed_count integer,
  failed_count integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::integer as total_images,
        COALESCE(SUM(di.file_size), 0)::bigint as total_size_bytes,
        ROUND(COALESCE(SUM(di.file_size), 0) / 1024.0 / 1024.0, 2) as total_size_mb,
        ROUND(COALESCE(AVG(di.file_size), 0) / 1024.0 / 1024.0, 2) as avg_file_size_mb,
        COUNT(CASE WHEN di.processing_status = 'pending' THEN 1 END)::integer as pending_count,
        COUNT(CASE WHEN di.processing_status = 'processing' THEN 1 END)::integer as processing_count,
        COUNT(CASE WHEN di.processing_status = 'completed' THEN 1 END)::integer as completed_count,
        COUNT(CASE WHEN di.processing_status = 'failed' THEN 1 END)::integer as failed_count
    FROM drone_images di
    WHERE di.farm_id = farm_id_param;
END;
$$;

-- Create function to validate image upload
CREATE OR REPLACE FUNCTION validate_image_upload(
  file_name text,
  file_size_bytes bigint,
  mime_type text
)
RETURNS boolean
LANGUAGE plpgsql
AS $$
DECLARE
  max_file_size bigint := 52428800; -- 50MB
  allowed_types text[] := ARRAY['image/jpeg', 'image/png', 'image/tiff', 'image/webp'];
BEGIN
  -- Check file size
  IF file_size_bytes > max_file_size THEN
    RAISE EXCEPTION 'File size exceeds maximum allowed size of 50MB';
  END IF;
  
  -- Check mime type
  IF NOT (mime_type = ANY(allowed_types)) THEN
    RAISE EXCEPTION 'File type not allowed. Allowed types: JPEG, PNG, TIFF, WebP';
  END IF;
  
  -- Check file extension
  IF NOT (LOWER(RIGHT(file_name, 4)) = ANY(ARRAY['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.webp'])) THEN
    RAISE EXCEPTION 'File extension not allowed';
  END IF;
  
  RETURN true;
END;
$$;

-- Create function to batch update processing status
CREATE OR REPLACE FUNCTION batch_update_processing_status(
  image_ids integer[],
  new_status text
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_count integer;
BEGIN
  UPDATE drone_images 
  SET processing_status = new_status::processing_status,
      updated_at = now()
  WHERE id = ANY(image_ids)
  AND EXISTS (
    SELECT 1 FROM farms f
    WHERE f.id = drone_images.farm_id
    AND f.user_id = auth.uid()
  );
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_farm_storage_stats(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_image_upload(text, bigint, text) TO authenticated;
GRANT EXECUTE ON FUNCTION batch_update_processing_status(integer[], text) TO authenticated;
