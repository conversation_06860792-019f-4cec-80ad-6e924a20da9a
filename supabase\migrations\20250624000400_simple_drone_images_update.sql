-- Simple migration that only modifies tables we have permission for

-- Add image_url column to drone_images table
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_url text;

-- Add updated_at column for tracking changes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- Add field_name column to drone_images for better organization
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS field_name text;

-- Add file_size column to track uploaded file sizes
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS file_size bigint;

-- Add metadata columns for better image management
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_width integer;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_height integer;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS camera_model text;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS drone_model text;

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_drone_images_updated_at 
    BEFORE UPDATE ON drone_images 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_drone_images_image_url ON drone_images(image_url);
CREATE INDEX IF NOT EXISTS idx_drone_images_field_name ON drone_images(field_name);
CREATE INDEX IF NOT EXISTS idx_drone_images_file_size ON drone_images(file_size);

-- Create function to calculate storage usage per farm
CREATE OR REPLACE FUNCTION get_farm_storage_usage(farm_id_param integer)
RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_size bigint;
BEGIN
    SELECT COALESCE(SUM(file_size), 0)
    INTO total_size
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN total_size;
END;
$$;

-- Create function to get image count per farm
CREATE OR REPLACE FUNCTION get_farm_image_count(farm_id_param integer)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_count integer;
BEGIN
    SELECT COUNT(*)
    INTO image_count
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN image_count;
END;
$$;

-- Create function to get storage stats for a farm
CREATE OR REPLACE FUNCTION get_farm_storage_stats(farm_id_param integer)
RETURNS TABLE(
  total_images integer,
  total_size_bytes bigint,
  total_size_mb numeric,
  avg_file_size_mb numeric,
  pending_count integer,
  processing_count integer,
  completed_count integer,
  failed_count integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::integer as total_images,
        COALESCE(SUM(di.file_size), 0)::bigint as total_size_bytes,
        ROUND(COALESCE(SUM(di.file_size), 0) / 1024.0 / 1024.0, 2) as total_size_mb,
        ROUND(COALESCE(AVG(di.file_size), 0) / 1024.0 / 1024.0, 2) as avg_file_size_mb,
        COUNT(CASE WHEN di.processing_status = 'pending' THEN 1 END)::integer as pending_count,
        COUNT(CASE WHEN di.processing_status = 'processing' THEN 1 END)::integer as processing_count,
        COUNT(CASE WHEN di.processing_status = 'completed' THEN 1 END)::integer as completed_count,
        COUNT(CASE WHEN di.processing_status = 'failed' THEN 1 END)::integer as failed_count
    FROM drone_images di
    WHERE di.farm_id = farm_id_param;
END;
$$;

-- Create function to generate unique filename
CREATE OR REPLACE FUNCTION generate_unique_filename(original_filename text)
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
    file_extension text;
    unique_name text;
BEGIN
    -- Extract file extension
    file_extension := LOWER(RIGHT(original_filename, 4));
    
    -- Generate unique filename with timestamp and random string
    unique_name := EXTRACT(EPOCH FROM NOW())::bigint::text || '_' || 
                   SUBSTR(MD5(RANDOM()::text), 1, 8) || file_extension;
    
    RETURN unique_name;
END;
$$;

-- Create function to batch update processing status
CREATE OR REPLACE FUNCTION batch_update_processing_status(
  image_ids integer[],
  new_status text
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_count integer;
BEGIN
  UPDATE drone_images 
  SET processing_status = new_status::processing_status,
      updated_at = now()
  WHERE id = ANY(image_ids)
  AND EXISTS (
    SELECT 1 FROM farms f
    WHERE f.id = drone_images.farm_id
    AND f.user_id = auth.uid()
  );
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$;

-- Update existing records to have field_name if null
UPDATE drone_images 
SET field_name = 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int
WHERE field_name IS NULL;

-- Add constraint to ensure field_name is not null for new records
ALTER TABLE drone_images ALTER COLUMN field_name SET DEFAULT 'Talhão A1';

-- Create trigger to auto-generate field_name if not provided
CREATE OR REPLACE FUNCTION auto_generate_field_name()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.field_name IS NULL OR NEW.field_name = '' THEN
        NEW.field_name := 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER auto_generate_field_name_trigger
    BEFORE INSERT ON drone_images
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_field_name();

-- Create view for drone images with farm information (security built-in)
CREATE OR REPLACE VIEW drone_images_with_farm AS
SELECT
    di.*,
    f.name as farm_name,
    f.owner_name as farm_owner,
    f.address as farm_address,
    f.crop_type as farm_crop_type,
    f.total_area as farm_total_area,
    f.user_id as farm_user_id
FROM drone_images di
JOIN farms f ON f.id = di.farm_id
WHERE f.user_id = auth.uid(); -- Security filter built into the view

-- Grant access to the view
GRANT SELECT ON drone_images_with_farm TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_farm_storage_usage(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_farm_image_count(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_farm_storage_stats(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION generate_unique_filename(text) TO authenticated;
GRANT EXECUTE ON FUNCTION batch_update_processing_status(integer[], text) TO authenticated;
