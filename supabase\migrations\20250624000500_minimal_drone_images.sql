-- Minimal migration that only adds essential columns to drone_images table
-- This avoids any permission issues with storage, views, or complex functions

-- Add essential columns to drone_images table
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS image_url text;
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS field_name text DEFAULT 'Talhão A1';
ALTER TABLE drone_images ADD COLUMN IF NOT EXISTS file_size bigint;

-- Create simple trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_drone_images_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_drone_images_updated_at ON drone_images;
CREATE TRIGGER update_drone_images_updated_at 
    BEFORE UPDATE ON drone_images 
    FOR EACH ROW 
    EXECUTE FUNCTION update_drone_images_updated_at();

-- Create simple trigger to auto-generate field_name if not provided
CREATE OR REPLACE FUNCTION auto_generate_field_name()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.field_name IS NULL OR NEW.field_name = '' THEN
        NEW.field_name := 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS auto_generate_field_name_trigger ON drone_images;
CREATE TRIGGER auto_generate_field_name_trigger
    BEFORE INSERT ON drone_images
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_field_name();

-- Update existing records to have field_name if null
UPDATE drone_images 
SET field_name = 'Talhão ' || CHR(65 + (RANDOM() * 3)::int) || (RANDOM() * 5 + 1)::int
WHERE field_name IS NULL;

-- Create basic indexes for performance
CREATE INDEX IF NOT EXISTS idx_drone_images_image_url ON drone_images(image_url);
CREATE INDEX IF NOT EXISTS idx_drone_images_field_name ON drone_images(field_name);
CREATE INDEX IF NOT EXISTS idx_drone_images_updated_at ON drone_images(updated_at);

-- Simple function to get image count per farm
CREATE OR REPLACE FUNCTION get_farm_image_count(farm_id_param integer)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_count integer;
    current_user_id uuid;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Check if user owns the farm
    IF NOT EXISTS (
        SELECT 1 FROM farms 
        WHERE id = farm_id_param 
        AND user_id = current_user_id
    ) THEN
        RETURN 0;
    END IF;
    
    -- Count images for the farm
    SELECT COUNT(*)
    INTO image_count
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN image_count;
END;
$$;

-- Simple function to get storage usage per farm
CREATE OR REPLACE FUNCTION get_farm_storage_usage(farm_id_param integer)
RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_size bigint;
    current_user_id uuid;
BEGIN
    -- Get current user ID
    current_user_id := auth.uid();
    
    -- Check if user owns the farm
    IF NOT EXISTS (
        SELECT 1 FROM farms 
        WHERE id = farm_id_param 
        AND user_id = current_user_id
    ) THEN
        RETURN 0;
    END IF;
    
    -- Calculate total size for the farm
    SELECT COALESCE(SUM(file_size), 0)
    INTO total_size
    FROM drone_images
    WHERE farm_id = farm_id_param;
    
    RETURN total_size;
END;
$$;
