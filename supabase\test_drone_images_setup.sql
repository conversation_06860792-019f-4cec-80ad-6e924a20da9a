-- <PERSON>ript para testar se a configuração de drone images está funcionando
-- Execute este script após a migração principal

-- 1. Verificar se as colunas foram adicionadas
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'drone_images' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Verificar se o bucket foi criado
SELECT id, name, public, file_size_limit, allowed_mime_types
FROM storage.buckets 
WHERE id = 'drone-images';

-- 3. Verificar se as políticas de storage foram criadas
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%drone%';

-- 4. Verificar se os índices foram criados
SELECT indexname, indexdef
FROM pg_indexes 
WHERE tablename = 'drone_images' 
AND schemaname = 'public'
AND indexname LIKE '%drone_images%';

-- 5. Veri<PERSON>r se as funções foram criadas
SELECT routine_name, routine_type, data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND (routine_name LIKE '%farm%' OR routine_name LIKE '%drone%' OR routine_name LIKE '%filename%')
ORDER BY routine_name;

-- 6. Verificar se os triggers foram criados
SELECT trigger_name, event_manipulation, event_object_table, action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'drone_images'
AND trigger_schema = 'public';

-- 7. Verificar se a view foi criada
SELECT table_name, view_definition
FROM information_schema.views 
WHERE table_name = 'drone_images_with_farm'
AND table_schema = 'public';

-- 8. Testar a função de geração de filename
SELECT generate_unique_filename('test_image.jpg') as generated_filename;

-- 9. Verificar permissões nas funções
SELECT routine_name, grantee, privilege_type
FROM information_schema.routine_privileges 
WHERE routine_schema = 'public' 
AND routine_name IN ('get_farm_storage_stats', 'generate_unique_filename', 'batch_update_processing_status');

-- 10. Verificar se RLS está habilitado nas tabelas necessárias
SELECT schemaname, tablename, rowsecurity
FROM pg_tables 
WHERE tablename IN ('drone_images', 'farms')
AND schemaname = 'public';

-- Resultado esperado:
-- ✅ Todas as colunas devem estar presentes na tabela drone_images
-- ✅ Bucket 'drone-images' deve existir e ser público
-- ✅ 4 políticas de storage devem estar criadas
-- ✅ Índices devem estar criados
-- ✅ Funções devem estar disponíveis
-- ✅ Triggers devem estar ativos
-- ✅ View deve estar criada
-- ✅ Função de filename deve retornar um nome único
-- ✅ Permissões devem estar concedidas
-- ✅ RLS deve estar habilitado
